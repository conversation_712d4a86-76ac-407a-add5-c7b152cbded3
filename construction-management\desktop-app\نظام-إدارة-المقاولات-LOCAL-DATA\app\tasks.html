<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المهام اليومية - نظام إدارة المقاولات</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🏗️ نظام إدارة المقاولات</h2>
                <p>والتطوير العقاري</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="./dashboard.html" class="nav-item">
                    <span class="icon">📊</span>
                    الصفحة الرئيسية
                </a>
                <a href="./projects.html" class="nav-item">
                    <span class="icon">🏗️</span>
                    إدارة المشاريع
                </a>
                <a href="./sales.html" class="nav-item">
                    <span class="icon">🏢</span>
                    مبيعات الشقق
                </a>
                <a href="./contractors.html" class="nav-item">
                    <span class="icon">👷</span>
                    المقاولين والمستخلصات
                </a>
                <a href="./suppliers.html" class="nav-item">
                    <span class="icon">🚚</span>
                    الموردين والفواتير
                </a>
                <a href="./purchases.html" class="nav-item">
                    <span class="icon">🛒</span>
                    المشتريات
                </a>
                <a href="./maintenance.html" class="nav-item">
                    <span class="icon">🔧</span>
                    الصيانة والتشغيل
                </a>
                <a href="./tasks.html" class="nav-item active">
                    <span class="icon">📋</span>
                    المهام اليومية
                </a>
                <a href="./reports.html" class="nav-item">
                    <span class="icon">📊</span>
                    التقارير المالية
                </a>
                <a href="./users.html" class="nav-item">
                    <span class="icon">👥</span>
                    إدارة المستخدمين
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>المهام اليومية</h1>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">أ</div>
                    <div>
                        <div id="userName">أحمد محمد</div>
                        <div style="font-size: 12px; color: #6b7280;" id="userRole">مدير النظام</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Page Header -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <div>
                        <h2 style="color: #374151; margin-bottom: 5px;">إدارة المهام اليومية</h2>
                        <p style="color: #6b7280;">تنظيم وتتبع المهام اليومية للفريق</p>
                    </div>
                    <button class="btn" onclick="openModal()">
                        ➕ مهمة جديدة
                    </button>
                </div>

                <!-- Today's Overview -->
                <div class="card" style="margin-bottom: 30px;">
                    <div class="card-header">
                        <h3 class="card-title">نظرة عامة - اليوم</h3>
                    </div>
                    <div class="card-content">
                        <div class="stats-row">
                            <div class="mini-stat">
                                <div class="mini-stat-icon">📋</div>
                                <div class="mini-stat-number">12</div>
                                <div class="mini-stat-label">إجمالي المهام</div>
                            </div>
                            <div class="mini-stat">
                                <div class="mini-stat-icon">✅</div>
                                <div class="mini-stat-number">8</div>
                                <div class="mini-stat-label">مكتملة</div>
                            </div>
                            <div class="mini-stat">
                                <div class="mini-stat-icon">⏳</div>
                                <div class="mini-stat-number">3</div>
                                <div class="mini-stat-label">قيد التنفيذ</div>
                            </div>
                            <div class="mini-stat">
                                <div class="mini-stat-icon">🚨</div>
                                <div class="mini-stat-number">1</div>
                                <div class="mini-stat-label">متأخرة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tasks by Priority -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                    <!-- High Priority Tasks -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🚨 مهام عاجلة</h3>
                        </div>
                        <div class="card-content">
                            <div style="margin-bottom: 15px; padding: 15px; background: #fef2f2; border-radius: 8px; border-right: 4px solid #ef4444;">
                                <div style="font-weight: bold; margin-bottom: 5px;">مراجعة تقرير المبيعات</div>
                                <div style="font-size: 12px; color: #6b7280;">مستحق: اليوم 5:00 م</div>
                                <div style="margin-top: 10px;">
                                    <button class="btn" style="padding: 5px 15px; font-size: 12px;">إكمال</button>
                                </div>
                            </div>
                            <div style="margin-bottom: 15px; padding: 15px; background: #fef2f2; border-radius: 8px; border-right: 4px solid #ef4444;">
                                <div style="font-weight: bold; margin-bottom: 5px;">اجتماع مع المقاول</div>
                                <div style="font-size: 12px; color: #6b7280;">مستحق: غداً 10:00 ص</div>
                                <div style="margin-top: 10px;">
                                    <button class="btn" style="padding: 5px 15px; font-size: 12px;">إكمال</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Today's Tasks -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">📅 مهام اليوم</h3>
                        </div>
                        <div class="card-content">
                            <div style="margin-bottom: 15px; padding: 15px; background: #f0f9ff; border-radius: 8px; border-right: 4px solid #3b82f6;">
                                <div style="font-weight: bold; margin-bottom: 5px;">متابعة طلبات الشراء</div>
                                <div style="font-size: 12px; color: #6b7280;">قيد التنفيذ</div>
                                <div style="margin-top: 10px;">
                                    <button class="btn btn-success" style="padding: 5px 15px; font-size: 12px;">إكمال</button>
                                </div>
                            </div>
                            <div style="margin-bottom: 15px; padding: 15px; background: #f0fdf4; border-radius: 8px; border-right: 4px solid #22c55e;">
                                <div style="font-weight: bold; margin-bottom: 5px;">إعداد تقرير يومي</div>
                                <div style="font-size: 12px; color: #6b7280;">مكتمل ✅</div>
                            </div>
                            <div style="padding: 15px; background: #f0f9ff; border-radius: 8px; border-right: 4px solid #3b82f6;">
                                <div style="font-weight: bold; margin-bottom: 5px;">مراجعة الفواتير</div>
                                <div style="font-size: 12px; color: #6b7280;">معلق</div>
                                <div style="margin-top: 10px;">
                                    <button class="btn" style="padding: 5px 15px; font-size: 12px;">بدء</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- All Tasks Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">جميع المهام (<span id="tasksCount">8</span>)</h3>
                    </div>
                    <div class="card-content" style="padding: 0;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المهمة</th>
                                    <th>المسؤول</th>
                                    <th>الأولوية</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="tasksBody">
                                <tr>
                                    <td>
                                        <div style="font-weight: bold;">مراجعة تقرير المبيعات</div>
                                        <div style="font-size: 12px; color: #6b7280;">مراجعة وتحليل أرقام المبيعات الشهرية</div>
                                    </td>
                                    <td>أحمد محمد</td>
                                    <td><span class="status-badge status-hold">عاجل</span></td>
                                    <td>2024-12-26</td>
                                    <td><span class="status-badge status-progress">قيد التنفيذ</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">إكمال</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: bold;">متابعة طلبات الشراء</div>
                                        <div style="font-size: 12px; color: #6b7280;">متابعة حالة طلبات الشراء المعلقة</div>
                                    </td>
                                    <td>فاطمة أحمد</td>
                                    <td><span class="status-badge status-progress">متوسط</span></td>
                                    <td>2024-12-26</td>
                                    <td><span class="status-badge status-progress">قيد التنفيذ</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">إكمال</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: bold;">إعداد تقرير يومي</div>
                                        <div style="font-size: 12px; color: #6b7280;">إعداد التقرير اليومي للإدارة</div>
                                    </td>
                                    <td>محمد علي</td>
                                    <td><span class="status-badge status-completed">منخفض</span></td>
                                    <td>2024-12-26</td>
                                    <td><span class="status-badge status-completed">مكتمل</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">عرض</button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">حذف</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: bold;">اجتماع مع المقاول</div>
                                        <div style="font-size: 12px; color: #6b7280;">مناقشة تقدم أعمال المشروع</div>
                                    </td>
                                    <td>سارة محمد</td>
                                    <td><span class="status-badge status-hold">عاجل</span></td>
                                    <td>2024-12-27</td>
                                    <td><span class="status-badge status-planning">معلق</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px;">بدء</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: bold;">مراجعة الفواتير</div>
                                        <div style="font-size: 12px; color: #6b7280;">مراجعة واعتماد الفواتير الواردة</div>
                                    </td>
                                    <td>خالد أحمد</td>
                                    <td><span class="status-badge status-progress">متوسط</span></td>
                                    <td>2024-12-28</td>
                                    <td><span class="status-badge status-planning">معلق</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px;">بدء</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="taskModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>مهمة جديدة</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="taskForm">
                <div class="form-group">
                    <label for="taskTitle">عنوان المهمة *</label>
                    <input type="text" id="taskTitle" required>
                </div>
                
                <div class="form-group">
                    <label for="taskDescription">الوصف</label>
                    <textarea id="taskDescription" rows="3"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="taskAssigned">المسؤول</label>
                        <input type="text" id="taskAssigned">
                    </div>
                    <div class="form-group">
                        <label for="taskPriority">الأولوية</label>
                        <select id="taskPriority">
                            <option value="low">منخفض</option>
                            <option value="medium">متوسط</option>
                            <option value="high">عالي</option>
                            <option value="urgent">عاجل</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="taskDueDate">تاريخ الاستحقاق</label>
                        <input type="date" id="taskDueDate">
                    </div>
                    <div class="form-group">
                        <label for="taskStatus">الحالة</label>
                        <select id="taskStatus">
                            <option value="pending">معلق</option>
                            <option value="in_progress">قيد التنفيذ</option>
                            <option value="completed">مكتمل</option>
                        </select>
                    </div>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn" style="background: #6b7280;" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <script src="supabase.js"></script>
    <script src="auth.js"></script>
    <script>
        function openModal() {
            document.getElementById('taskModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('taskModal').style.display = 'none';
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('currentUser');
                window.location.href = './index.html';
            }
        }

        // تحقق من تسجيل الدخول
        window.addEventListener('load', function() {
            const currentUser = localStorage.getItem('currentUser');
            if (!currentUser) {
                window.location.href = './index.html';
                return;
            }
            
            const user = JSON.parse(currentUser);
            document.getElementById('userName').textContent = user.name;
            document.getElementById('userAvatar').textContent = user.name.charAt(0);
            
            const roleNames = {
                admin: 'مدير النظام',
                manager: 'مدير',
                accountant: 'محاسب'
            };
            document.getElementById('userRole').textContent = roleNames[user.role] || user.role;
        });

        // إضافة أنماط الإحصائيات
        const style = document.createElement('style');
        style.textContent = `
            .stats-row {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 30px;
            }
            
            .mini-stat {
                background: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            
            .mini-stat-icon {
                font-size: 1.5rem;
                margin-bottom: 10px;
            }
            
            .mini-stat-number {
                font-size: 1.5rem;
                font-weight: bold;
                color: #2563eb;
                margin-bottom: 5px;
            }
            
            .mini-stat-label {
                color: #6b7280;
                font-size: 0.9rem;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
