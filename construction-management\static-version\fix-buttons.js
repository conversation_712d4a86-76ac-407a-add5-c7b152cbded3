// ملف إصلاح الأزرار لجميع الصفحات
// يجب إضافة هذا الملف لجميع الصفحات التي تحتاج إصلاح

// التأكد من وجود قاعدة البيانات
if (typeof db === 'undefined') {
    console.warn('قاعدة البيانات غير متاحة، سيتم استخدام الوضع المحلي');
    window.db = {
        isOnline: false,
        async getData(table) {
            const data = localStorage.getItem('construction_management_' + table);
            return { success: true, data: data ? JSON.parse(data) : [] };
        },
        async insertData(table, data) {
            const existingData = JSON.parse(localStorage.getItem('construction_management_' + table) || '[]');
            const newData = { ...data, id: Date.now() };
            existingData.push(newData);
            localStorage.setItem('construction_management_' + table, JSON.stringify(existingData));
            return { success: true, data: newData };
        },
        async updateData(table, id, data) {
            const existingData = JSON.parse(localStorage.getItem('construction_management_' + table) || '[]');
            const index = existingData.findIndex(item => item.id == id);
            if (index !== -1) {
                existingData[index] = { ...existingData[index], ...data };
                localStorage.setItem('construction_management_' + table, JSON.stringify(existingData));
                return { success: true, data: existingData[index] };
            }
            return { success: false, error: 'العنصر غير موجود' };
        },
        async deleteData(table, id) {
            const existingData = JSON.parse(localStorage.getItem('construction_management_' + table) || '[]');
            const filteredData = existingData.filter(item => item.id != id);
            localStorage.setItem('construction_management_' + table, JSON.stringify(filteredData));
            return { success: true };
        }
    };
}

// دالة التحقق من المصادقة (إذا لم تكن موجودة)
if (typeof checkAuthAndUpdateUI === 'undefined') {
    window.checkAuthAndUpdateUI = function() {
        const currentUser = localStorage.getItem('currentUser');
        if (!currentUser) {
            window.location.href = './index.html';
            return;
        }
        
        const user = JSON.parse(currentUser);
        const userNameEl = document.getElementById('userName');
        const userAvatarEl = document.getElementById('userAvatar');
        const userRoleEl = document.getElementById('userRole');
        
        if (userNameEl) userNameEl.textContent = user.name || 'مستخدم';
        if (userAvatarEl) userAvatarEl.textContent = (user.name || 'م').charAt(0);
        
        if (userRoleEl) {
            const roleNames = {
                admin: 'مدير النظام',
                manager: 'مدير',
                accountant: 'محاسب'
            };
            userRoleEl.textContent = roleNames[user.role] || user.role;
        }
    };
}

// دالة تسجيل الخروج (إذا لم تكن موجودة)
if (typeof logout === 'undefined') {
    window.logout = function() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            localStorage.removeItem('currentUser');
            window.location.href = './index.html';
        }
    };
}

// دوال عامة للنوافذ المنبثقة
if (typeof openModal === 'undefined') {
    window.openModal = function() {
        const modal = document.querySelector('.modal');
        if (modal) {
            modal.style.display = 'block';
        }
    };
}

if (typeof closeModal === 'undefined') {
    window.closeModal = function() {
        const modal = document.querySelector('.modal');
        if (modal) {
            modal.style.display = 'none';
        }
    };
}

// إضافة مستمع لإغلاق النوافذ عند النقر خارجها
document.addEventListener('click', function(e) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
});

// تشغيل التحقق من المصادقة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkAuthAndUpdateUI();
});

console.log('تم تحميل ملف إصلاح الأزرار بنجاح');
