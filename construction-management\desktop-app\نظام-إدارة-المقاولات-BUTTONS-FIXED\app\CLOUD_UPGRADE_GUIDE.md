# 🌐 دليل الترقية للوضع السحابي

## ✅ تم تحديث النظام للعمل مع Supabase!

### 🔧 **ما تم تحديثه:**

#### **1. نظام المصادقة المحسن:**
- ✅ **تسجيل دخول مع Supabase** - أولوية للوضع السحابي
- ✅ **نظام احتياطي محلي** - في حالة انقطاع الاتصال
- ✅ **مؤشر حالة الاتصال** - يظهر في لوحة التحكم

#### **2. قاعدة البيانات السحابية:**
- ✅ **11 جدول كامل** - جميع الوحدات
- ✅ **بيانات تجريبية** - جاهزة للاستخدام
- ✅ **نظام أمان** - Row Level Security

#### **3. مزامنة البيانات:**
- ✅ **حفظ سحابي** - عند توفر الاتصال
- ✅ **حفظ محلي** - عند انقطاع الاتصال
- ✅ **تبديل تلقائي** - بين الوضعين

---

## 🚀 خطوات إكمال الترقية

### الخطوة 1: تشغيل Database Schema (5 دقائق)

#### 1.1 الدخول إلى Supabase
1. **اذهب إلى**: https://supabase.com/dashboard/project/xlpcpojmatiejxizukkz
2. **اضغط "SQL Editor"** في الشريط الجانبي
3. **اضغط "New query"**

#### 1.2 تشغيل الكود
**انسخ والصق هذا الكود بالكامل:**

```sql
-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255),
    role VARCHAR(50) DEFAULT 'employee',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المشاريع
CREATE TABLE IF NOT EXISTS projects (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    location VARCHAR(255),
    start_date DATE,
    end_date DATE,
    budget DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'planning',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المبيعات
CREATE TABLE IF NOT EXISTS sales (
    id BIGSERIAL PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(50),
    customer_email VARCHAR(255),
    apartment_number VARCHAR(50),
    project_name VARCHAR(255),
    sale_price DECIMAL(15,2),
    down_payment DECIMAL(15,2),
    installment_plan VARCHAR(100),
    sale_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المقاولين
CREATE TABLE IF NOT EXISTS contractors (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    company VARCHAR(255),
    phone VARCHAR(50),
    email VARCHAR(255),
    specialty VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المستخلصات
CREATE TABLE IF NOT EXISTS extracts (
    id BIGSERIAL PRIMARY KEY,
    contractor_id BIGINT REFERENCES contractors(id),
    project_id BIGINT REFERENCES projects(id),
    extract_number VARCHAR(50),
    amount DECIMAL(15,2),
    work_description TEXT,
    extract_date DATE,
    payment_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الموردين
CREATE TABLE IF NOT EXISTS suppliers (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    company VARCHAR(255),
    phone VARCHAR(50),
    email VARCHAR(255),
    address TEXT,
    category VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الفواتير
CREATE TABLE IF NOT EXISTS invoices (
    id BIGSERIAL PRIMARY KEY,
    supplier_id BIGINT REFERENCES suppliers(id),
    project_id BIGINT REFERENCES projects(id),
    invoice_number VARCHAR(50),
    amount DECIMAL(15,2),
    invoice_date DATE,
    due_date DATE,
    payment_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المشتريات
CREATE TABLE IF NOT EXISTS purchases (
    id BIGSERIAL PRIMARY KEY,
    supplier_id BIGINT REFERENCES suppliers(id),
    project_id BIGINT REFERENCES projects(id),
    item_name VARCHAR(255),
    quantity DECIMAL(10,2),
    unit_price DECIMAL(15,2),
    total_amount DECIMAL(15,2),
    purchase_date DATE,
    delivery_date DATE,
    status VARCHAR(50) DEFAULT 'ordered',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إدراج بيانات تجريبية للمستخدمين
INSERT INTO users (email, full_name, role) VALUES
('<EMAIL>', 'أحمد محمد', 'admin'),
('<EMAIL>', 'فاطمة أحمد', 'manager'),
('<EMAIL>', 'محمد علي', 'accountant')
ON CONFLICT (email) DO NOTHING;

-- إدراج بيانات تجريبية للمشاريع
INSERT INTO projects (name, description, location, start_date, budget, status) VALUES
('مشروع الأندلس السكني', 'مجمع سكني متكامل يحتوي على 200 وحدة سكنية', 'الرياض - حي الأندلس', '2024-01-15', ********, 'in_progress'),
('برج النيل التجاري', 'برج تجاري بارتفاع 25 طابق في وسط المدينة', 'القاهرة - وسط البلد', '2024-03-01', ********, 'planning'),
('مجمع الزهراء السكني', 'مشروع سكني فاخر يضم 150 فيلا', 'جدة - حي الزهراء', '2023-06-01', ********, 'completed')
ON CONFLICT DO NOTHING;

-- تفعيل Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE contractors ENABLE ROW LEVEL SECURITY;
ALTER TABLE extracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchases ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات الأمان
CREATE POLICY "Enable all operations for authenticated users" ON users FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON projects FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON sales FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON contractors FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON extracts FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON suppliers FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON invoices FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON purchases FOR ALL USING (true);
```

4. **اضغط "Run"** لتشغيل الكود
5. **انتظر رسالة "Success"** ✅

### الخطوة 2: إعداد Authentication (3 دقائق)

#### 2.1 إعداد Site URL
1. **اذهب إلى "Authentication" > "Settings"**
2. **في "Site URL" أضف رابط موقعك:**
   ```
   https://your-netlify-site.netlify.app
   ```
3. **في "Redirect URLs" أضف:**
   ```
   https://your-netlify-site.netlify.app/**
   ```
4. **اضغط "Save"**

#### 2.2 إنشاء مستخدم Admin
1. **اذهب إلى "Authentication" > "Users"**
2. **اضغط "Invite user"**
3. **أدخل:**
   ```
   Email: <EMAIL>
   Password: admin123
   ```
4. **بعد إنشاء المستخدم، اضغط عليه**
5. **في "Raw User Meta Data" أضف:**
   ```json
   {
     "role": "admin",
     "full_name": "مدير النظام"
   }
   ```
6. **اضغط "Save"**

### الخطوة 3: النشر والاختبار (2 دقيقة)

#### 3.1 النشر
1. **انشر النظام المحدث** على Netlify
2. **انتظر انتهاء النشر**

#### 3.2 الاختبار
1. **افتح الموقع**
2. **ابحث عن مؤشر الحالة** في لوحة التحكم:
   - 🟢 **"متصل"** = نجح الربط مع Supabase ✅
   - 🟡 **"محلي"** = يعمل في الوضع المحلي ⚠️

#### 3.3 تسجيل الدخول
1. **استخدم الحساب الجديد:**
   ```
   البريد: <EMAIL>
   كلمة المرور: admin123
   ```
2. **يجب أن تدخل بنجاح**
3. **تأكد من ظهور مؤشر "متصل" 🟢**

---

## 🎯 النتيجة المتوقعة

### ✅ **بعد إكمال الترقية:**

#### **🟢 الوضع السحابي (مع الاتصال):**
- **مؤشر "متصل"** في لوحة التحكم
- **حفظ البيانات** في Supabase
- **مشاركة البيانات** بين المستخدمين
- **مزامنة فورية** للتحديثات

#### **🟡 الوضع المحلي (بدون اتصال):**
- **مؤشر "محلي"** في لوحة التحكم
- **حفظ البيانات** في localStorage
- **استمرار العمل** بدون إنترنت
- **تبديل تلقائي** عند عودة الاتصال

---

## 🔧 استكشاف الأخطاء

### **مؤشر يظهر "محلي" 🟡**
**الأسباب المحتملة:**
1. لم يتم تشغيل Database Schema
2. خطأ في إعدادات Authentication
3. مشكلة في الشبكة

**الحل:**
1. تحقق من تشغيل SQL في Supabase
2. تحقق من Site URL في Authentication
3. تحقق من وحدة التحكم للأخطاء (F12)

### **خطأ في تسجيل الدخول**
**الحل:**
1. تأكد من إنشاء المستخدم في Supabase
2. تحقق من كلمة المرور
3. تحقق من إعدادات Authentication

---

## 🎉 مميزات الوضع السحابي

### ✅ **ما ستحصل عليه:**
- **🌐 وصول من أي مكان** - جميع الأجهزة
- **👥 مشاركة البيانات** - عدة مستخدمين
- **☁️ نسخ احتياطي تلقائي** - في السحابة
- **🔄 مزامنة فورية** - تحديثات مباشرة
- **🔒 أمان متقدم** - Row Level Security
- **📊 إحصائيات حقيقية** - من قاعدة البيانات

### 🔄 **النظام الذكي:**
- **تبديل تلقائي** بين السحابي والمحلي
- **استمرار العمل** حتى مع انقطاع الإنترنت
- **مؤشر واضح** لحالة الاتصال
- **حفظ آمن** في جميع الأحوال

---

## 📋 قائمة المراجعة النهائية

- [ ] تم تشغيل Database Schema في Supabase
- [ ] تم إعداد Site URL في Authentication
- [ ] تم إنشاء مستخدم admin
- [ ] تم نشر النظام المحدث
- [ ] يظهر مؤشر "متصل" 🟢 في لوحة التحكم
- [ ] تم اختبار تسجيل الدخول
- [ ] تم اختبار إضافة البيانات
- [ ] تم اختبار مشاركة البيانات

**عند إكمال جميع النقاط، النظام جاهز للاستخدام السحابي!** ✅

---

**🚀 النظام الآن يعمل في الوضع السحابي مع إمكانية الوصول من أي مكان ومشاركة البيانات!**
