-- إعداد سريع لقاعدة البيانات
-- انسخ والصق هذا الكود في Supabase SQL Editor

-- إن<PERSON>اء جدول المستخدمين
CREATE TABLE IF NOT EXISTS public.users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'employee',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إن<PERSON>اء جدول المشاريع
CREATE TABLE IF NOT EXISTS public.projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    location VARCHAR(255),
    start_date DATE,
    end_date DATE,
    budget DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'planning',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الشقق
CREATE TABLE IF NOT EXISTS public.apartments (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES public.projects(id),
    apartment_number VARCHAR(50) NOT NULL,
    floor_number INTEGER,
    area DECIMAL(10,2),
    price DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'available',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المبيعات
CREATE TABLE IF NOT EXISTS public.sales (
    id SERIAL PRIMARY KEY,
    apartment_id INTEGER REFERENCES public.apartments(id),
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(50),
    customer_email VARCHAR(255),
    sale_price DECIMAL(15,2),
    down_payment DECIMAL(15,2),
    installment_plan VARCHAR(255),
    sale_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المقاولين
CREATE TABLE IF NOT EXISTS public.contractors (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    email VARCHAR(255),
    specialty VARCHAR(255),
    address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المستخلصات
CREATE TABLE IF NOT EXISTS public.extracts (
    id SERIAL PRIMARY KEY,
    contractor_id INTEGER REFERENCES public.contractors(id),
    project_id INTEGER REFERENCES public.projects(id),
    extract_number VARCHAR(50),
    amount DECIMAL(15,2),
    description TEXT,
    extract_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الموردين
CREATE TABLE IF NOT EXISTS public.suppliers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    email VARCHAR(255),
    category VARCHAR(255),
    address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الفواتير
CREATE TABLE IF NOT EXISTS public.invoices (
    id SERIAL PRIMARY KEY,
    supplier_id INTEGER REFERENCES public.suppliers(id),
    invoice_number VARCHAR(50),
    amount DECIMAL(15,2),
    description TEXT,
    invoice_date DATE,
    due_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المشتريات
CREATE TABLE IF NOT EXISTS public.purchases (
    id SERIAL PRIMARY KEY,
    supplier_id INTEGER REFERENCES public.suppliers(id),
    project_id INTEGER REFERENCES public.projects(id),
    item_name VARCHAR(255) NOT NULL,
    quantity INTEGER,
    unit_price DECIMAL(10,2),
    total_amount DECIMAL(15,2),
    purchase_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول مهام الصيانة
CREATE TABLE IF NOT EXISTS public.maintenance_tasks (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES public.projects(id),
    task_title VARCHAR(255) NOT NULL,
    description TEXT,
    priority VARCHAR(50) DEFAULT 'medium',
    assigned_to VARCHAR(255),
    due_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المهام اليومية
CREATE TABLE IF NOT EXISTS public.daily_tasks (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    priority VARCHAR(50) DEFAULT 'medium',
    assigned_to VARCHAR(255),
    due_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إدراج بيانات تجريبية للمشاريع
INSERT INTO public.projects (name, description, location, start_date, end_date, budget, status) VALUES
('مشروع الأندلس السكني', 'مجمع سكني متكامل يضم 100 شقة', 'القاهرة الجديدة', '2024-01-15', '2025-12-31', 50000000, 'in_progress'),
('برج النيل التجاري', 'برج تجاري بارتفاع 20 طابق', 'وسط البلد', '2024-06-01', '2026-05-31', 75000000, 'planning'),
('مجمع الزهراء السكني', 'مجمع سكني فاخر', 'الشيخ زايد', '2023-03-01', '2024-02-29', 60000000, 'completed')
ON CONFLICT DO NOTHING;

-- إدراج بيانات تجريبية للموردين
INSERT INTO public.suppliers (name, phone, email, category, address) VALUES
('شركة مواد البناء المتحدة', '01234567890', '<EMAIL>', 'مواد البناء', 'القاهرة الجديدة'),
('مؤسسة الحديد والصلب', '01098765432', '<EMAIL>', 'حديد وصلب', 'حلوان'),
('شركة الكهرباء الحديثة', '01555666777', '<EMAIL>', 'مواد كهربائية', 'مدينة نصر')
ON CONFLICT DO NOTHING;

-- إدراج بيانات تجريبية للمقاولين
INSERT INTO public.contractors (name, phone, email, specialty, address) VALUES
('مقاولات الإنشاء المتطورة', '01111222333', '<EMAIL>', 'إنشاءات عامة', 'المعادي'),
('شركة التشطيبات الفاخرة', '01444555666', '<EMAIL>', 'تشطيبات', 'الزمالك'),
('مؤسسة الأعمال الكهربائية', '01777888999', '<EMAIL>', 'أعمال كهربائية', 'مصر الجديدة')
ON CONFLICT DO NOTHING;

-- تفعيل Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.apartments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contractors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.extracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.maintenance_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_tasks ENABLE ROW LEVEL SECURITY;

-- إنشاء policies للوصول العام (يمكن تخصيصها لاحقاً)
CREATE POLICY "Enable all operations for authenticated users" ON public.projects FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON public.apartments FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON public.sales FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON public.contractors FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON public.extracts FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON public.suppliers FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON public.invoices FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON public.purchases FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON public.maintenance_tasks FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON public.daily_tasks FOR ALL USING (true);

-- رسالة نجاح
SELECT 'تم إعداد قاعدة البيانات بنجاح! 🎉' as message;

