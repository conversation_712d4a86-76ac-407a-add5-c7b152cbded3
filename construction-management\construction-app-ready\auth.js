// ملف المصادقة المشترك

// تحقق من تسجيل الدخول
function checkAuth() {
    const currentUser = localStorage.getItem('currentUser');
    if (!currentUser) {
        window.location.href = 'index.html';
        return null;
    }
    return JSON.parse(currentUser);
}

// تحديث معلومات المستخدم في الواجهة
function updateUserInfo() {
    const user = checkAuth();
    if (user) {
        const userNameElement = document.getElementById('userName');
        const userAvatarElement = document.getElementById('userAvatar');
        const userRoleElement = document.getElementById('userRole');
        
        if (userNameElement) userNameElement.textContent = user.name;
        if (userAvatarElement) userAvatarElement.textContent = user.name.charAt(0);
        
        if (userRoleElement) {
            const roleNames = {
                admin: 'مدير النظام',
                manager: 'مدير',
                accountant: 'محاسب'
            };
            userRoleElement.textContent = roleNames[user.role] || user.role;
        }
    }
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    }
}

// تحقق من الصلاحيات
function hasPermission(requiredRoles) {
    const user = checkAuth();
    if (!user) return false;
    
    if (Array.isArray(requiredRoles)) {
        return requiredRoles.includes(user.role);
    }
    
    return user.role === requiredRoles;
}

// تحقق من المصادقة وتحديث الواجهة
function checkAuthAndUpdateUI() {
    const user = checkAuth();
    if (user) {
        updateUserInfo();
        return user;
    }
    return null;
}

// إخفاء/إظهار العناصر حسب الصلاحيات
function hideElementsBasedOnRole() {
    const user = checkAuth();
    if (!user) return;
    
    // إخفاء إدارة المستخدمين للمحاسبين
    if (user.role === 'accountant') {
        const usersLink = document.querySelector('a[href="users.html"]');
        if (usersLink) {
            usersLink.style.display = 'none';
        }
    }
    
    // يمكن إضافة المزيد من القواعد هنا
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحقق من المصادقة في جميع الصفحات عدا صفحة تسجيل الدخول
    if (!window.location.pathname.includes('index.html') && 
        !window.location.pathname.endsWith('/')) {
        checkAuthAndUpdateUI();
        hideElementsBasedOnRole();
    }
});
