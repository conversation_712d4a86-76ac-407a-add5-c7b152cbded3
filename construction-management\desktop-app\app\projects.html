<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشاريع - نظام إدارة المقاولات</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }
        
        .table tr:hover {
            background: #f8fafc;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-planning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-progress {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-hold {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .search-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            padding: 10px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            min-width: 150px;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .close {
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🏗️ نظام إدارة المقاولات</h2>
                <p>والتطوير العقاري</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="./dashboard.html" class="nav-item">
                    <span class="icon">📊</span>
                    الصفحة الرئيسية
                </a>
                <a href="./projects.html" class="nav-item active">
                    <span class="icon">🏗️</span>
                    إدارة المشاريع
                </a>
                <a href="./sales.html" class="nav-item">
                    <span class="icon">🏢</span>
                    مبيعات الشقق
                </a>
                <a href="./contractors.html" class="nav-item">
                    <span class="icon">👷</span>
                    المقاولين والمستخلصات
                </a>
                <a href="./suppliers.html" class="nav-item">
                    <span class="icon">🚚</span>
                    الموردين والفواتير
                </a>
                <a href="./purchases.html" class="nav-item">
                    <span class="icon">🛒</span>
                    المشتريات
                </a>
                <a href="./maintenance.html" class="nav-item">
                    <span class="icon">🔧</span>
                    الصيانة والتشغيل
                </a>
                <a href="./tasks.html" class="nav-item">
                    <span class="icon">📋</span>
                    المهام اليومية
                </a>
                <a href="./reports.html" class="nav-item">
                    <span class="icon">📊</span>
                    التقارير المالية
                </a>
                <a href="./users.html" class="nav-item">
                    <span class="icon">👥</span>
                    إدارة المستخدمين
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>إدارة المشاريع</h1>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">أ</div>
                    <div>
                        <div id="userName">أحمد محمد</div>
                        <div style="font-size: 12px; color: #6b7280;" id="userRole">مدير النظام</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Page Header -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <div>
                        <h2 style="color: #374151; margin-bottom: 5px;">إدارة المشاريع</h2>
                        <p style="color: #6b7280;">إدارة جميع مشاريع الشركة</p>
                    </div>
                    <button class="btn" onclick="openModal()">
                        ➕ مشروع جديد
                    </button>
                </div>

                <!-- Search and Filter -->
                <div class="card" style="margin-bottom: 20px;">
                    <div class="card-content" style="padding: 20px;">
                        <div class="search-bar">
                            <input type="text" class="search-input" placeholder="البحث في المشاريع..." id="searchInput">
                            <select class="filter-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="planning">تخطيط</option>
                                <option value="in_progress">قيد التنفيذ</option>
                                <option value="completed">مكتمل</option>
                                <option value="on_hold">متوقف</option>
                            </select>
                            <button class="btn" onclick="filterProjects()">بحث</button>
                        </div>
                    </div>
                </div>

                <!-- Projects Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">قائمة المشاريع (<span id="projectCount">3</span>)</h3>
                    </div>
                    <div class="card-content" style="padding: 0;">
                        <table class="table" id="projectsTable">
                            <thead>
                                <tr>
                                    <th>اسم المشروع</th>
                                    <th>الموقع</th>
                                    <th>تاريخ البداية</th>
                                    <th>الميزانية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="projectsBody">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="projectModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">مشروع جديد</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="projectForm">
                <div class="form-group">
                    <label for="projectName">اسم المشروع *</label>
                    <input type="text" id="projectName" required>
                </div>
                
                <div class="form-group">
                    <label for="projectDescription">الوصف</label>
                    <textarea id="projectDescription" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="projectLocation">الموقع</label>
                    <input type="text" id="projectLocation">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="startDate">تاريخ البداية</label>
                        <input type="date" id="startDate">
                    </div>
                    <div class="form-group">
                        <label for="endDate">تاريخ النهاية</label>
                        <input type="date" id="endDate">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="budget">الميزانية</label>
                        <input type="number" id="budget" placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label for="status">الحالة</label>
                        <select id="status">
                            <option value="planning">تخطيط</option>
                            <option value="in_progress">قيد التنفيذ</option>
                            <option value="completed">مكتمل</option>
                            <option value="on_hold">متوقف</option>
                        </select>
                    </div>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn" style="background: #6b7280;" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- إضافة ملفات الإصلاح المطلوبة -->
    <script src="fix-everything.js"></script>
    <script>
        // بيانات المشاريع (محاكاة قاعدة البيانات)
        let projects = [
            {
                id: 1,
                name: 'مشروع الأندلس السكني',
                description: 'مجمع سكني متكامل يضم 100 شقة',
                location: 'القاهرة الجديدة',
                startDate: '2024-01-15',
                endDate: '2025-12-31',
                budget: 50000000,
                status: 'in_progress'
            },
            {
                id: 2,
                name: 'برج النيل التجاري',
                description: 'برج تجاري بارتفاع 20 طابق',
                location: 'وسط البلد',
                startDate: '2024-06-01',
                endDate: '2026-05-31',
                budget: 75000000,
                status: 'planning'
            },
            {
                id: 3,
                name: 'مجمع الزهراء السكني',
                description: 'مجمع سكني فاخر',
                location: 'الشيخ زايد',
                startDate: '2023-03-01',
                endDate: '2024-02-29',
                budget: 60000000,
                status: 'completed'
            }
        ];

        let editingProjectId = null;

        // عرض المشاريع
        function displayProjects(projectsToShow = projects) {
            const tbody = document.getElementById('projectsBody');
            tbody.innerHTML = '';

            projectsToShow.forEach(project => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: bold;">${project.name}</div>
                        <div style="font-size: 12px; color: #6b7280;">${project.description || ''}</div>
                    </td>
                    <td>${project.location || '-'}</td>
                    <td>${project.startDate ? formatDate(project.startDate) : '-'}</td>
                    <td>${project.budget ? formatCurrency(project.budget) : '-'}</td>
                    <td>${getStatusBadge(project.status)}</td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;" onclick="editProject(${project.id})">تعديل</button>
                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteProject(${project.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('projectCount').textContent = projectsToShow.length;
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-EG').format(amount) + ' ج.م';
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-EG');
        }

        // شارة الحالة
        function getStatusBadge(status) {
            const statusMap = {
                planning: { label: 'تخطيط', class: 'status-planning' },
                in_progress: { label: 'قيد التنفيذ', class: 'status-progress' },
                completed: { label: 'مكتمل', class: 'status-completed' },
                on_hold: { label: 'متوقف', class: 'status-hold' }
            };
            
            const statusInfo = statusMap[status] || { label: status, class: 'status-planning' };
            return `<span class="status-badge ${statusInfo.class}">${statusInfo.label}</span>`;
        }

        // فتح النافذة المنبثقة
        function openModal() {
            document.getElementById('projectModal').style.display = 'block';
            document.getElementById('modalTitle').textContent = 'مشروع جديد';
            document.getElementById('projectForm').reset();
            editingProjectId = null;
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('projectModal').style.display = 'none';
        }

        // تعديل مشروع
        function editProject(id) {
            const project = projects.find(p => p.id === id);
            if (project) {
                editingProjectId = id;
                document.getElementById('modalTitle').textContent = 'تعديل المشروع';
                document.getElementById('projectName').value = project.name;
                document.getElementById('projectDescription').value = project.description || '';
                document.getElementById('projectLocation').value = project.location || '';
                document.getElementById('startDate').value = project.startDate || '';
                document.getElementById('endDate').value = project.endDate || '';
                document.getElementById('budget').value = project.budget || '';
                document.getElementById('status').value = project.status;
                document.getElementById('projectModal').style.display = 'block';
            }
        }

        // حذف مشروع
        function deleteProject(id) {
            if (confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
                projects = projects.filter(p => p.id !== id);
                displayProjects();
            }
        }

        // تصفية المشاريع
        function filterProjects() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            let filtered = projects;

            if (searchTerm) {
                filtered = filtered.filter(project =>
                    project.name.toLowerCase().includes(searchTerm) ||
                    (project.location && project.location.toLowerCase().includes(searchTerm))
                );
            }

            if (statusFilter) {
                filtered = filtered.filter(project => project.status === statusFilter);
            }

            displayProjects(filtered);
        }

        // حفظ المشروع
        document.getElementById('projectForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const projectData = {
                name: document.getElementById('projectName').value,
                description: document.getElementById('projectDescription').value,
                location: document.getElementById('projectLocation').value,
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value,
                budget: parseFloat(document.getElementById('budget').value) || 0,
                status: document.getElementById('status').value
            };

            try {
                let result;
                if (editingProjectId) {
                    // تعديل مشروع موجود
                    result = await db.updateData('projects', editingProjectId, projectData);
                    if (result.success) {
                        const index = projects.findIndex(p => p.id === editingProjectId);
                        if (index !== -1) {
                            projects[index] = { ...projects[index], ...projectData };
                        }
                        alert('تم تحديث المشروع بنجاح!');
                    }
                } else {
                    // إضافة مشروع جديد
                    result = await db.insertData('projects', projectData);
                    if (result.success) {
                        projects.push(result.data);
                        alert('تم إضافة المشروع بنجاح!');
                    }
                }

                if (result.success) {
                    displayProjects();
                    closeModal();
                    document.getElementById('projectForm').reset();
                    editingProjectId = null;
                } else {
                    alert('حدث خطأ: ' + result.error);
                }
            } catch (error) {
                console.error('خطأ في حفظ المشروع:', error);
                alert('حدث خطأ في حفظ المشروع');
            }
        });

        // حذف مشروع
        async function deleteProject(id) {
            if (confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
                try {
                    const result = await db.deleteData('projects', id);
                    if (result.success) {
                        projects = projects.filter(p => p.id !== id);
                        displayProjects();
                        alert('تم حذف المشروع بنجاح!');
                    } else {
                        alert('حدث خطأ في حذف المشروع: ' + result.error);
                    }
                } catch (error) {
                    console.error('خطأ في حذف المشروع:', error);
                    alert('حدث خطأ في حذف المشروع');
                }
            }
        }

        // تعديل مشروع
        function editProject(id) {
            const project = projects.find(p => p.id === id);
            if (project) {
                editingProjectId = id;

                // ملء النموذج بالبيانات
                document.getElementById('projectName').value = project.name || '';
                document.getElementById('projectDescription').value = project.description || '';
                document.getElementById('projectLocation').value = project.location || '';
                document.getElementById('startDate').value = project.startDate || '';
                document.getElementById('endDate').value = project.endDate || '';
                document.getElementById('budget').value = project.budget || '';
                document.getElementById('status').value = project.status || 'planning';

                // تغيير عنوان النافذة
                document.querySelector('#projectModal .modal-header h3').textContent = 'تعديل المشروع';

                openModal();
            }
        }

        // تحميل المشاريع من قاعدة البيانات
        async function loadProjects() {
            try {
                const result = await db.getData('projects');
                if (result.success) {
                    projects = result.data || [];
                    displayProjects();
                }
            } catch (error) {
                console.error('خطأ في تحميل المشاريع:', error);
            }
        }

        // تهيئة الصفحة
        window.addEventListener('load', function() {
            checkAuthAndUpdateUI();
            loadProjects(); // تحميل المشاريع من قاعدة البيانات

            // إضافة مستمع للبحث المباشر
            document.getElementById('searchInput').addEventListener('input', filterProjects);
            document.getElementById('statusFilter').addEventListener('change', filterProjects);
        });

        // إغلاق النافذة عند النقر خارجها
        window.addEventListener('click', function(e) {
            const modal = document.getElementById('projectModal');
            if (e.target === modal) {
                closeModal();
            }
        });

        // التأكد من تحميل قاعدة البيانات
        if (typeof db === 'undefined') {
            console.error('قاعدة البيانات غير محملة - تحقق من ملف supabase.js');
        } else {
            console.log('تم تحميل قاعدة البيانات بنجاح');
        }
    </script>
</body>
</html>
