// إصلاح نهائي للأزرار مع البيانات المحلية
console.log('🔧 تحميل الإصلاح النهائي للأزرار...');

// انتظار تحميل كل شيء
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeFinalFix, 2000);
});

if (document.readyState === 'complete') {
    setTimeout(initializeFinalFix, 2000);
}

function initializeFinalFix() {
    console.log('🚀 بدء الإصلاح النهائي...');
    
    // إصلاح جميع الأزرار
    fixAllButtonsWithLocalData();
    
    // إصلاح النماذج
    fixAllFormsWithLocalData();
    
    // إضافة معالجات الأحداث
    addEventListeners();
    
    console.log('✅ تم الإصلاح النهائي');
}

function fixAllButtonsWithLocalData() {
    console.log('🔧 إصلاح الأزرار مع البيانات المحلية...');
    
    // إصلاح أزرار إضافة المشاريع
    const addProjectBtns = document.querySelectorAll('button[onclick*="showAddProject"], .btn-primary');
    addProjectBtns.forEach(btn => {
        if (btn.textContent.includes('مشروع') || btn.textContent.includes('➕')) {
            btn.onclick = null;
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                showAddProjectModal();
            });
            console.log('✅ تم إصلاح زر إضافة مشروع');
        }
    });
    
    // إصلاح أزرار إضافة المبيعات
    const addSaleBtns = document.querySelectorAll('button[onclick*="showAddSale"]');
    addSaleBtns.forEach(btn => {
        btn.onclick = null;
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddSaleModal();
        });
        console.log('✅ تم إصلاح زر إضافة مبيعة');
    });
    
    // إصلاح أزرار إضافة المقاولين
    const addContractorBtns = document.querySelectorAll('button[onclick*="showAddContractor"]');
    addContractorBtns.forEach(btn => {
        btn.onclick = null;
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddContractorModal();
        });
        console.log('✅ تم إصلاح زر إضافة مقاول');
    });
    
    // إصلاح أزرار إضافة الموردين
    const addSupplierBtns = document.querySelectorAll('button[onclick*="showAddSupplier"]');
    addSupplierBtns.forEach(btn => {
        btn.onclick = null;
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddSupplierModal();
        });
        console.log('✅ تم إصلاح زر إضافة مورد');
    });
    
    // إصلاح أزرار إضافة المشتريات
    const addPurchaseBtns = document.querySelectorAll('button[onclick*="showAddPurchase"]');
    addPurchaseBtns.forEach(btn => {
        btn.onclick = null;
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddPurchaseModal();
        });
        console.log('✅ تم إصلاح زر إضافة مشترى');
    });
}

function fixAllFormsWithLocalData() {
    console.log('📝 إصلاح النماذج مع البيانات المحلية...');
    
    // إصلاح نموذج المشاريع
    const projectForm = document.getElementById('addProjectForm');
    if (projectForm) {
        projectForm.onsubmit = null;
        projectForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await handleProjectSubmit(this);
        });
    }
    
    // إصلاح نموذج المبيعات
    const saleForm = document.getElementById('addSaleForm');
    if (saleForm) {
        saleForm.onsubmit = null;
        saleForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await handleSaleSubmit(this);
        });
    }
    
    // إصلاح نموذج المقاولين
    const contractorForm = document.getElementById('addContractorForm');
    if (contractorForm) {
        contractorForm.onsubmit = null;
        contractorForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await handleContractorSubmit(this);
        });
    }
    
    // إصلاح نموذج الموردين
    const supplierForm = document.getElementById('addSupplierForm');
    if (supplierForm) {
        supplierForm.onsubmit = null;
        supplierForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await handleSupplierSubmit(this);
        });
    }
    
    // إصلاح نموذج المشتريات
    const purchaseForm = document.getElementById('addPurchaseForm');
    if (purchaseForm) {
        purchaseForm.onsubmit = null;
        purchaseForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await handlePurchaseSubmit(this);
        });
    }
}

// دوال إظهار النماذج
function showAddProjectModal() {
    console.log('📝 إظهار نموذج إضافة مشروع');
    const modal = document.getElementById('addProjectModal');
    if (modal) {
        modal.style.display = 'block';
    } else {
        alert('نموذج إضافة المشروع غير متاح');
    }
}

function showAddSaleModal() {
    console.log('📝 إظهار نموذج إضافة مبيعة');
    const modal = document.getElementById('addSaleModal');
    if (modal) {
        modal.style.display = 'block';
    } else {
        alert('نموذج إضافة المبيعة غير متاح');
    }
}

function showAddContractorModal() {
    console.log('📝 إظهار نموذج إضافة مقاول');
    const modal = document.getElementById('addContractorModal');
    if (modal) {
        modal.style.display = 'block';
    } else {
        alert('نموذج إضافة المقاول غير متاح');
    }
}

function showAddSupplierModal() {
    console.log('📝 إظهار نموذج إضافة مورد');
    const modal = document.getElementById('addSupplierModal');
    if (modal) {
        modal.style.display = 'block';
    } else {
        alert('نموذج إضافة المورد غير متاح');
    }
}

function showAddPurchaseModal() {
    console.log('📝 إظهار نموذج إضافة مشترى');
    const modal = document.getElementById('addPurchaseModal');
    if (modal) {
        modal.style.display = 'block';
    } else {
        alert('نموذج إضافة المشترى غير متاح');
    }
}

// دوال معالجة النماذج
async function handleProjectSubmit(form) {
    try {
        console.log('💾 حفظ مشروع جديد...');
        
        const formData = new FormData(form);
        const projectData = {
            name: formData.get('name') || 'مشروع جديد',
            description: formData.get('description') || '',
            location: formData.get('location') || '',
            status: formData.get('status') || 'planning',
            budget: parseFloat(formData.get('budget')) || 0
        };
        
        const result = await window.db.add('projects', projectData);
        
        if (result) {
            alert('✅ تم حفظ المشروع بنجاح!');
            form.reset();
            closeModal();
            
            // إعادة تحميل البيانات
            if (typeof loadProjects === 'function') {
                loadProjects();
            }
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ المشروع:', error);
        alert('❌ خطأ في حفظ المشروع: ' + error.message);
    }
}

async function handleSaleSubmit(form) {
    try {
        console.log('💾 حفظ مبيعة جديدة...');
        
        const formData = new FormData(form);
        const saleData = {
            customer_name: formData.get('customer_name') || 'عميل جديد',
            apartment_number: formData.get('apartment_number') || '',
            project_name: formData.get('project_name') || '',
            price: parseFloat(formData.get('price')) || 0,
            payment_method: formData.get('payment_method') || 'cash'
        };
        
        const result = await window.db.add('sales', saleData);
        
        if (result) {
            alert('✅ تم حفظ المبيعة بنجاح!');
            form.reset();
            closeModal();
            
            if (typeof loadSales === 'function') {
                loadSales();
            }
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ المبيعة:', error);
        alert('❌ خطأ في حفظ المبيعة: ' + error.message);
    }
}

async function handleContractorSubmit(form) {
    try {
        console.log('💾 حفظ مقاول جديد...');
        
        const formData = new FormData(form);
        const contractorData = {
            name: formData.get('name') || 'مقاول جديد',
            specialty: formData.get('specialty') || '',
            phone: formData.get('phone') || '',
            email: formData.get('email') || ''
        };
        
        const result = await window.db.add('contractors', contractorData);
        
        if (result) {
            alert('✅ تم حفظ المقاول بنجاح!');
            form.reset();
            closeModal();
            
            if (typeof loadContractors === 'function') {
                loadContractors();
            }
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ المقاول:', error);
        alert('❌ خطأ في حفظ المقاول: ' + error.message);
    }
}

async function handleSupplierSubmit(form) {
    try {
        console.log('💾 حفظ مورد جديد...');
        
        const formData = new FormData(form);
        const supplierData = {
            name: formData.get('name') || 'مورد جديد',
            category: formData.get('category') || '',
            phone: formData.get('phone') || '',
            email: formData.get('email') || ''
        };
        
        const result = await window.db.add('suppliers', supplierData);
        
        if (result) {
            alert('✅ تم حفظ المورد بنجاح!');
            form.reset();
            closeModal();
            
            if (typeof loadSuppliers === 'function') {
                loadSuppliers();
            }
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ المورد:', error);
        alert('❌ خطأ في حفظ المورد: ' + error.message);
    }
}

async function handlePurchaseSubmit(form) {
    try {
        console.log('💾 حفظ مشترى جديد...');
        
        const formData = new FormData(form);
        const purchaseData = {
            item_name: formData.get('item_name') || 'مشترى جديد',
            supplier_name: formData.get('supplier_name') || '',
            quantity: parseInt(formData.get('quantity')) || 1,
            price: parseFloat(formData.get('price')) || 0
        };
        
        const result = await window.db.add('purchases', purchaseData);
        
        if (result) {
            alert('✅ تم حفظ المشترى بنجاح!');
            form.reset();
            closeModal();
            
            if (typeof loadPurchases === 'function') {
                loadPurchases();
            }
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ المشترى:', error);
        alert('❌ خطأ في حفظ المشترى: ' + error.message);
    }
}

function closeModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
    });
}

function addEventListeners() {
    // إضافة معالجات إغلاق النماذج
    const closeButtons = document.querySelectorAll('.close, .btn-secondary');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', closeModal);
    });
    
    // إغلاق النماذج عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
        }
    });
}

// إعادة تشغيل الإصلاحات كل 10 ثوان
setInterval(() => {
    const buttonsNeedFix = document.querySelectorAll('button[onclick]');
    if (buttonsNeedFix.length > 0) {
        console.log('🔄 إعادة إصلاح الأزرار...');
        fixAllButtonsWithLocalData();
    }
}, 10000);

console.log('✅ تم تحميل الإصلاح النهائي للأزرار');
console.log('💡 جميع الأزرار تعمل الآن مع البيانات المحلية');
