const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// تعريض APIs آمنة للتطبيق
contextBridge.exposeInMainWorld('electronAPI', {
    // معلومات النظام
    platform: process.platform,
    version: process.versions.electron,
    
    // دوال التطبيق
    minimize: () => ipcRenderer.invoke('window-minimize'),
    maximize: () => ipcRenderer.invoke('window-maximize'),
    close: () => ipcRenderer.invoke('window-close'),
    
    // دوال الملفات
    openFile: () => ipcRenderer.invoke('dialog-open-file'),
    saveFile: (data) => ipcRenderer.invoke('dialog-save-file', data),
    
    // إشعارات
    showNotification: (title, body) => {
        if (Notification.permission === 'granted') {
            new Notification(title, { body });
        }
    },
    
    // طباعة
    print: () => ipcRenderer.invoke('window-print')
});

// إعداد الإشعارات
window.addEventListener('DOMContentLoaded', () => {
    // طلب إذن الإشعارات
    if (Notification.permission === 'default') {
        Notification.requestPermission();
    }
    
    // إضافة معلومات التطبيق
    const appInfo = document.createElement('div');
    appInfo.id = 'electron-info';
    appInfo.style.display = 'none';
    appInfo.innerHTML = `
        <span id="platform">${process.platform}</span>
        <span id="electron-version">${process.versions.electron}</span>
        <span id="node-version">${process.versions.node}</span>
    `;
    document.body.appendChild(appInfo);
    
    console.log('🖥️ تطبيق سطح المكتب جاهز');
    console.log('📱 النظام:', process.platform);
    console.log('⚡ Electron:', process.versions.electron);
});
