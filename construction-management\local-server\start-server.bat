@echo off
chcp 65001 >nul
title نظام إدارة المقاولات - خادم محلي

echo.
echo ========================================
echo    نظام إدارة المقاولات والتطوير العقاري
echo ========================================
echo.

echo 🔧 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo.
    echo 📥 يرجى تحميل وتثبيت Node.js من:
    echo https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js مثبت بنجاح
echo.

echo 📦 التحقق من المكتبات...
if not exist "node_modules" (
    echo 🔄 تثبيت المكتبات المطلوبة...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات!
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
    echo.
)

echo 🚀 بدء تشغيل الخادم...
echo.
echo 📋 معلومات مهمة:
echo    - العنوان المحلي: http://localhost:3000
echo    - للوصول من أجهزة أخرى: http://[IP-ADDRESS]:3000
echo    - لإيقاف الخادم: اضغط Ctrl+C
echo.
echo ⏳ انتظار بدء الخادم...
echo.

npm start

echo.
echo 👋 تم إيقاف الخادم
pause
