{"name": "construction-management-app", "version": "1.0.0", "description": "نظام إدارة المقاولات والتطوير العقاري", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["construction", "management", "real-estate", "arabic"], "author": "Construction Management System", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "dependencies": {"electron-updater": "^6.1.7"}, "build": {"appId": "com.construction.management", "productName": "نظام إدارة المقاولات", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "app/**/*", "node_modules/**/*"], "win": {"target": [{"target": "portable", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة المقاولات"}}}