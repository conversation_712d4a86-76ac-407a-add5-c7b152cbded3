{"name": "construction-management-server", "version": "1.0.0", "description": "<PERSON>ادم محلي لنظام إدارة المقاولات", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "pkg server.js --target node18-win-x64 --output construction-server.exe", "build-all": "pkg server.js --targets node18-win-x64,node18-macos-x64,node18-linux-x64", "install-deps": "npm install", "setup": "npm install && npm run copy-files", "copy-files": "node copy-files.js"}, "keywords": ["construction", "management", "server", "local", "arabic"], "author": "Construction Management System", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.2", "pkg": "^5.8.1"}, "pkg": {"assets": ["public/**/*"], "outputPath": "dist"}}