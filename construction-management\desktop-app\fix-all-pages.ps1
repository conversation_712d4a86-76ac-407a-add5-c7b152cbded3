# إصلاح جميع صفحات التطبيق لإضافة electron-fix.js

Write-Host "🔧 إصلاح جميع صفحات التطبيق..." -ForegroundColor Yellow

$pages = @(
    "sales.html",
    "contractors.html", 
    "suppliers.html",
    "purchases.html",
    "reports.html",
    "users.html",
    "tasks.html",
    "maintenance.html"
)

foreach ($page in $pages) {
    $filePath = "app\$page"
    
    if (Test-Path $filePath) {
        Write-Host "📝 إصلاح $page..." -ForegroundColor Cyan
        
        # قراءة محتوى الملف
        $content = Get-Content $filePath -Raw -Encoding UTF8
        
        # البحث عن السطر المطلوب وإضافة electron-fix.js
        if ($content -match 'force-online\.js') {
            $content = $content -replace 'force-online\.js">', 'force-online.js"></script>' + "`n" + '    <script src="electron-fix.js">'
            
            # حفظ الملف
            $content | Out-File $filePath -Encoding UTF8 -NoNewline
            
            # نسخ إلى التطبيق المحمول
            Copy-Item $filePath "portable-app\app\" -Force
            
            Write-Host "✅ تم إصلاح $page" -ForegroundColor Green
        } else {
            Write-Host "⚠️ لم يتم العثور على force-online.js في $page" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ لم يتم العثور على $page" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "✅ تم إصلاح جميع الصفحات!" -ForegroundColor Green
Write-Host "📦 تم تحديث التطبيق المحمول" -ForegroundColor Green

# إنشاء ملف ZIP محدث
Write-Host "📦 إنشاء ملف ZIP محدث..." -ForegroundColor Yellow
if (Test-Path "نظام-إدارة-المقاولات-محمول-FIXED.zip") {
    Remove-Item "نظام-إدارة-المقاولات-محمول-FIXED.zip" -Force
}

Compress-Archive -Path "portable-app\*" -DestinationPath "نظام-إدارة-المقاولات-محمول-FIXED.zip" -Force

Write-Host "✅ تم إنشاء ملف ZIP محدث: نظام-إدارة-المقاولات-محمول-FIXED.zip" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 التطبيق جاهز للاستخدام مع إصلاح الأزرار!" -ForegroundColor Green
