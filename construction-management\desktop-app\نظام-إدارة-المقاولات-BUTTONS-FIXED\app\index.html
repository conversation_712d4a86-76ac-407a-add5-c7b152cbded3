<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المقاولات - تسجيل الدخول</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="logo">
                <div class="logo-icon">🏗️</div>
                <h1>نظام إدارة المقاولات</h1>
                <p>والتطوير العقاري</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" required placeholder="أدخل البريد الإلكتروني">
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="password-input">
                        <input type="password" id="password" name="password" required placeholder="أدخل كلمة المرور">
                        <button type="button" class="toggle-password" onclick="togglePassword()">👁️</button>
                    </div>
                </div>
                
                <div class="form-options">
                    <label class="checkbox">
                        <input type="checkbox" id="remember">
                        <span>تذكرني</span>
                    </label>
                    <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                </div>
                
                <button type="submit" class="login-btn">تسجيل الدخول</button>
            </form>
            
            <div class="demo-accounts">
                <h3>حسابات تجريبية:</h3>
                <div class="demo-account" onclick="fillDemoAccount('admin')">
                    <strong>مدير النظام:</strong> <EMAIL> / admin123
                </div>
                <div class="demo-account" onclick="fillDemoAccount('manager')">
                    <strong>مدير:</strong> <EMAIL> / manager123
                </div>
                <div class="demo-account" onclick="fillDemoAccount('accountant')">
                    <strong>محاسب:</strong> <EMAIL> / acc123
                </div>
            </div>
            
            <div class="register-link">
                <p>ليس لديك حساب؟ <a href="register.html">إنشاء حساب جديد</a></p>
            </div>
        </div>
    </div>

    <script src="supabase.js"></script>
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        function fillDemoAccount(type) {
            const accounts = {
                admin: { email: '<EMAIL>', password: 'admin123' },
                manager: { email: '<EMAIL>', password: 'manager123' },
                accountant: { email: '<EMAIL>', password: 'acc123' }
            };
            
            document.getElementById('email').value = accounts[type].email;
            document.getElementById('password').value = accounts[type].password;
        }

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            // إظهار رسالة تحميل
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'جاري تسجيل الدخول...';
            submitBtn.disabled = true;

            try {
                // محاولة تسجيل الدخول مع Supabase أولاً
                const result = await db.signIn(email, password);

                if (result.success) {
                    // حفظ بيانات المستخدم
                    localStorage.setItem('currentUser', JSON.stringify({
                        id: result.user.id,
                        email: result.user.email,
                        role: result.user.role || 'employee',
                        name: result.user.full_name || result.user.name || 'مستخدم'
                    }));

                    // توجيه إلى لوحة التحكم
                    alert('تم تسجيل الدخول بنجاح!');
                    window.location.href = './dashboard.html';
                } else {
                    // في حالة فشل Supabase، استخدم النظام المحلي
                    const validAccounts = {
                        '<EMAIL>': { password: 'admin123', role: 'admin', name: 'أحمد محمد' },
                        '<EMAIL>': { password: 'manager123', role: 'manager', name: 'فاطمة أحمد' },
                        '<EMAIL>': { password: 'acc123', role: 'accountant', name: 'محمد علي' }
                    };

                    if (validAccounts[email] && validAccounts[email].password === password) {
                        localStorage.setItem('currentUser', JSON.stringify({
                            id: Date.now(),
                            email: email,
                            role: validAccounts[email].role,
                            name: validAccounts[email].name
                        }));

                        alert('تم تسجيل الدخول بنجاح! (وضع محلي)');
                        window.location.href = './dashboard.html';
                    } else {
                        alert('البريد الإلكتروني أو كلمة المرور غير صحيحة');
                    }
                }
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                alert('حدث خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى.');
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });

        // تحقق من وجود مستخدم مسجل دخول
        window.addEventListener('load', function() {
            const currentUser = localStorage.getItem('currentUser');
            if (currentUser) {
                window.location.href = 'dashboard.html';
            }
        });
    </script>
</body>
</html>
