<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموردين والفواتير - نظام إدارة المقاولات</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🏗️ نظام إدارة المقاولات</h2>
                <p>والتطوير العقاري</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.html" class="nav-item">
                    <span class="icon">📊</span>
                    الصفحة الرئيسية
                </a>
                <a href="projects.html" class="nav-item">
                    <span class="icon">🏗️</span>
                    إدارة المشاريع
                </a>
                <a href="sales.html" class="nav-item">
                    <span class="icon">🏢</span>
                    مبيعات الشقق
                </a>
                <a href="contractors.html" class="nav-item">
                    <span class="icon">👷</span>
                    المقاولين والمستخلصات
                </a>
                <a href="suppliers.html" class="nav-item active">
                    <span class="icon">🚚</span>
                    الموردين والفواتير
                </a>
                <a href="purchases.html" class="nav-item">
                    <span class="icon">🛒</span>
                    المشتريات
                </a>
                <a href="maintenance.html" class="nav-item">
                    <span class="icon">🔧</span>
                    الصيانة والتشغيل
                </a>
                <a href="tasks.html" class="nav-item">
                    <span class="icon">📋</span>
                    المهام اليومية
                </a>
                <a href="reports.html" class="nav-item">
                    <span class="icon">📊</span>
                    التقارير المالية
                </a>
                <a href="users.html" class="nav-item">
                    <span class="icon">👥</span>
                    إدارة المستخدمين
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>الموردين والفواتير</h1>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">أ</div>
                    <div>
                        <div id="userName">أحمد محمد</div>
                        <div style="font-size: 12px; color: #6b7280;" id="userRole">مدير النظام</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Tabs -->
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; border-bottom: 2px solid #e5e7eb;">
                        <button class="tab-btn active" onclick="switchTab('suppliers')">الموردين</button>
                        <button class="tab-btn" onclick="switchTab('invoices')">الفواتير</button>
                    </div>
                </div>

                <!-- Suppliers Tab -->
                <div id="suppliersTab" class="tab-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <div>
                            <h2 style="color: #374151; margin-bottom: 5px;">إدارة الموردين</h2>
                            <p style="color: #6b7280;">إدارة بيانات الموردين والتصنيفات</p>
                        </div>
                        <button class="btn" onclick="openSupplierModal()">
                            ➕ مورد جديد
                        </button>
                    </div>

                    <!-- Suppliers Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">قائمة الموردين (<span id="suppliersCount">4</span>)</h3>
                        </div>
                        <div class="card-content" style="padding: 0;">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>اسم المورد</th>
                                        <th>التصنيف</th>
                                        <th>رقم الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>العنوان</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="suppliersBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Invoices Tab -->
                <div id="invoicesTab" class="tab-content" style="display: none;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <div>
                            <h2 style="color: #374151; margin-bottom: 5px;">إدارة الفواتير</h2>
                            <p style="color: #6b7280;">إدارة فواتير الموردين والمدفوعات</p>
                        </div>
                        <button class="btn" onclick="openInvoiceModal()">
                            ➕ فاتورة جديدة
                        </button>
                    </div>

                    <!-- Invoices Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">قائمة الفواتير (<span id="invoicesCount">5</span>)</h3>
                        </div>
                        <div class="card-content" style="padding: 0;">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>المورد</th>
                                        <th>المبلغ</th>
                                        <th>تاريخ الفاتورة</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoicesBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Supplier Modal -->
    <div id="supplierModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="supplierModalTitle">مورد جديد</h3>
                <span class="close" onclick="closeSupplierModal()">&times;</span>
            </div>
            
            <form id="supplierForm">
                <div class="form-group">
                    <label for="supplierName">اسم المورد *</label>
                    <input type="text" id="supplierName" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="supplierPhone">رقم الهاتف</label>
                        <input type="tel" id="supplierPhone">
                    </div>
                    <div class="form-group">
                        <label for="supplierEmail">البريد الإلكتروني</label>
                        <input type="email" id="supplierEmail">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="supplierCategory">التصنيف</label>
                    <input type="text" id="supplierCategory" placeholder="مثال: مواد البناء">
                </div>
                
                <div class="form-group">
                    <label for="supplierAddress">العنوان</label>
                    <textarea id="supplierAddress" rows="3"></textarea>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn" style="background: #6b7280;" onclick="closeSupplierModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Invoice Modal -->
    <div id="invoiceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="invoiceModalTitle">فاتورة جديدة</h3>
                <span class="close" onclick="closeInvoiceModal()">&times;</span>
            </div>
            
            <form id="invoiceForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="invoiceNumber">رقم الفاتورة *</label>
                        <input type="text" id="invoiceNumber" required>
                    </div>
                    <div class="form-group">
                        <label for="invoiceSupplier">المورد *</label>
                        <select id="invoiceSupplier" required>
                            <option value="">اختر المورد</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="invoiceAmount">المبلغ *</label>
                        <input type="number" id="invoiceAmount" required placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label for="invoiceDate">تاريخ الفاتورة</label>
                        <input type="date" id="invoiceDate">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="invoiceDueDate">تاريخ الاستحقاق</label>
                        <input type="date" id="invoiceDueDate">
                    </div>
                    <div class="form-group">
                        <label for="invoiceStatus">الحالة</label>
                        <select id="invoiceStatus">
                            <option value="pending">معلق</option>
                            <option value="paid">مدفوع</option>
                            <option value="overdue">متأخر</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="invoiceDescription">الوصف</label>
                    <textarea id="invoiceDescription" rows="3" placeholder="وصف الفاتورة"></textarea>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn" style="background: #6b7280;" onclick="closeInvoiceModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- إضافة ملفات الإصلاح المطلوبة -->
    <script src="supabase.js"></script>
    <script src="auth.js"></script>
    <script src="fix-buttons.js"></script>
    <script src="button-fix-universal.js"></script>
    <script>
        // بيانات الموردين
        let suppliers = [
            {
                id: 1,
                name: 'شركة مواد البناء المتحدة',
                phone: '01234567890',
                email: '<EMAIL>',
                category: 'مواد البناء',
                address: 'القاهرة الجديدة، التجمع الأول'
            },
            {
                id: 2,
                name: 'مؤسسة الحديد والصلب',
                phone: '01098765432',
                email: '<EMAIL>',
                category: 'حديد وصلب',
                address: 'حلوان، القاهرة'
            },
            {
                id: 3,
                name: 'شركة الكهرباء الحديثة',
                phone: '01555666777',
                email: '<EMAIL>',
                category: 'مواد كهربائية',
                address: 'مدينة نصر، القاهرة'
            },
            {
                id: 4,
                name: 'مؤسسة السباكة المتطورة',
                phone: '01777888999',
                email: '<EMAIL>',
                category: 'مواد سباكة',
                address: 'المعادي، القاهرة'
            }
        ];

        // بيانات الفواتير
        let invoices = [
            {
                id: 1,
                invoiceNumber: 'INV-2024-001',
                supplierId: 1,
                supplierName: 'شركة مواد البناء المتحدة',
                amount: 250000,
                description: 'أسمنت ورمل',
                invoiceDate: '2024-12-20',
                dueDate: '2025-01-20',
                status: 'pending'
            },
            {
                id: 2,
                invoiceNumber: 'INV-2024-002',
                supplierId: 2,
                supplierName: 'مؤسسة الحديد والصلب',
                amount: 180000,
                description: 'حديد تسليح',
                invoiceDate: '2024-12-19',
                dueDate: '2025-01-19',
                status: 'paid'
            },
            {
                id: 3,
                invoiceNumber: 'INV-2024-003',
                supplierId: 3,
                supplierName: 'شركة الكهرباء الحديثة',
                amount: 95000,
                description: 'كابلات ومفاتيح كهربائية',
                invoiceDate: '2024-12-18',
                dueDate: '2025-01-18',
                status: 'overdue'
            },
            {
                id: 4,
                invoiceNumber: 'INV-2024-004',
                supplierId: 4,
                supplierName: 'مؤسسة السباكة المتطورة',
                amount: 75000,
                description: 'مواسير ووصلات',
                invoiceDate: '2024-12-17',
                dueDate: '2025-01-17',
                status: 'pending'
            },
            {
                id: 5,
                invoiceNumber: 'INV-2024-005',
                supplierId: 1,
                supplierName: 'شركة مواد البناء المتحدة',
                amount: 320000,
                description: 'طوب وبلاط',
                invoiceDate: '2024-12-16',
                dueDate: '2025-01-16',
                status: 'paid'
            }
        ];

        let editingSupplierId = null;
        let editingInvoiceId = null;

        // تبديل التبويبات
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            document.getElementById(tabName + 'Tab').style.display = 'block';
            event.target.classList.add('active');
        }

        // عرض الموردين
        function displaySuppliers() {
            const tbody = document.getElementById('suppliersBody');
            tbody.innerHTML = '';

            suppliers.forEach(supplier => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: bold;">${supplier.name}</div>
                    </td>
                    <td>${supplier.category || '-'}</td>
                    <td>${supplier.phone || '-'}</td>
                    <td>${supplier.email || '-'}</td>
                    <td>${supplier.address || '-'}</td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;" onclick="editSupplier(${supplier.id})">تعديل</button>
                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteSupplier(${supplier.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('suppliersCount').textContent = suppliers.length;
            updateSupplierSelect();
        }

        // عرض الفواتير
        function displayInvoices() {
            const tbody = document.getElementById('invoicesBody');
            tbody.innerHTML = '';

            invoices.forEach(invoice => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${invoice.invoiceNumber}</td>
                    <td>${invoice.supplierName}</td>
                    <td>${formatCurrency(invoice.amount)}</td>
                    <td>${formatDate(invoice.invoiceDate)}</td>
                    <td>${formatDate(invoice.dueDate)}</td>
                    <td>${getStatusBadge(invoice.status)}</td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;" onclick="editInvoice(${invoice.id})">تعديل</button>
                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteInvoice(${invoice.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('invoicesCount').textContent = invoices.length;
        }

        // تحديث قائمة الموردين في النموذج
        function updateSupplierSelect() {
            const select = document.getElementById('invoiceSupplier');
            select.innerHTML = '<option value="">اختر المورد</option>';
            
            suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.id;
                option.textContent = supplier.name;
                select.appendChild(option);
            });
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-EG').format(amount) + ' ج.م';
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-EG');
        }

        // شارة الحالة
        function getStatusBadge(status) {
            const statusMap = {
                pending: { label: 'معلق', class: 'status-planning' },
                paid: { label: 'مدفوع', class: 'status-completed' },
                overdue: { label: 'متأخر', class: 'status-hold' }
            };
            
            const statusInfo = statusMap[status] || { label: status, class: 'status-planning' };
            return `<span class="status-badge ${statusInfo.class}">${statusInfo.label}</span>`;
        }

        // نوافذ الموردين
        function openSupplierModal() {
            document.getElementById('supplierModal').style.display = 'block';
            document.getElementById('supplierModalTitle').textContent = 'مورد جديد';
            document.getElementById('supplierForm').reset();
            editingSupplierId = null;
        }

        function closeSupplierModal() {
            document.getElementById('supplierModal').style.display = 'none';
        }

        function editSupplier(id) {
            const supplier = suppliers.find(s => s.id === id);
            if (supplier) {
                editingSupplierId = id;
                document.getElementById('supplierModalTitle').textContent = 'تعديل المورد';
                document.getElementById('supplierName').value = supplier.name;
                document.getElementById('supplierPhone').value = supplier.phone || '';
                document.getElementById('supplierEmail').value = supplier.email || '';
                document.getElementById('supplierCategory').value = supplier.category || '';
                document.getElementById('supplierAddress').value = supplier.address || '';
                document.getElementById('supplierModal').style.display = 'block';
            }
        }

        function deleteSupplier(id) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                suppliers = suppliers.filter(s => s.id !== id);
                displaySuppliers();
            }
        }

        // نوافذ الفواتير
        function openInvoiceModal() {
            document.getElementById('invoiceModal').style.display = 'block';
            document.getElementById('invoiceModalTitle').textContent = 'فاتورة جديدة';
            document.getElementById('invoiceForm').reset();
            editingInvoiceId = null;
        }

        function closeInvoiceModal() {
            document.getElementById('invoiceModal').style.display = 'none';
        }

        function editInvoice(id) {
            const invoice = invoices.find(i => i.id === id);
            if (invoice) {
                editingInvoiceId = id;
                document.getElementById('invoiceModalTitle').textContent = 'تعديل الفاتورة';
                document.getElementById('invoiceNumber').value = invoice.invoiceNumber;
                document.getElementById('invoiceSupplier').value = invoice.supplierId;
                document.getElementById('invoiceAmount').value = invoice.amount;
                document.getElementById('invoiceDescription').value = invoice.description || '';
                document.getElementById('invoiceDate').value = invoice.invoiceDate || '';
                document.getElementById('invoiceDueDate').value = invoice.dueDate || '';
                document.getElementById('invoiceStatus').value = invoice.status;
                document.getElementById('invoiceModal').style.display = 'block';
            }
        }

        function deleteInvoice(id) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                invoices = invoices.filter(i => i.id !== id);
                displayInvoices();
            }
        }

        // حفظ المورد
        document.getElementById('supplierForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const supplierData = {
                name: document.getElementById('supplierName').value,
                phone: document.getElementById('supplierPhone').value,
                email: document.getElementById('supplierEmail').value,
                category: document.getElementById('supplierCategory').value,
                address: document.getElementById('supplierAddress').value
            };

            if (editingSupplierId) {
                const index = suppliers.findIndex(s => s.id === editingSupplierId);
                if (index !== -1) {
                    suppliers[index] = { ...suppliers[index], ...supplierData };
                }
            } else {
                const newSupplier = {
                    id: Date.now(),
                    ...supplierData
                };
                suppliers.push(newSupplier);
            }

            displaySuppliers();
            closeSupplierModal();
        });

        // حفظ الفاتورة
        document.getElementById('invoiceForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const supplierId = parseInt(document.getElementById('invoiceSupplier').value);
            const supplier = suppliers.find(s => s.id === supplierId);

            const invoiceData = {
                invoiceNumber: document.getElementById('invoiceNumber').value,
                supplierId: supplierId,
                supplierName: supplier ? supplier.name : '',
                amount: parseFloat(document.getElementById('invoiceAmount').value),
                description: document.getElementById('invoiceDescription').value,
                invoiceDate: document.getElementById('invoiceDate').value,
                dueDate: document.getElementById('invoiceDueDate').value,
                status: document.getElementById('invoiceStatus').value
            };

            if (editingInvoiceId) {
                const index = invoices.findIndex(i => i.id === editingInvoiceId);
                if (index !== -1) {
                    invoices[index] = { ...invoices[index], ...invoiceData };
                }
            } else {
                const newInvoice = {
                    id: Date.now(),
                    ...invoiceData
                };
                invoices.push(newInvoice);
            }

            displayInvoices();
            closeInvoiceModal();
        });

        // تهيئة الصفحة
        window.addEventListener('load', function() {
            checkAuthAndUpdateUI();
            displaySuppliers();
            displayInvoices();
        });

        // إضافة أنماط التبويبات
        const style = document.createElement('style');
        style.textContent = `
            .tab-btn {
                padding: 12px 24px;
                background: none;
                border: none;
                border-bottom: 2px solid transparent;
                cursor: pointer;
                font-size: 16px;
                color: #6b7280;
                transition: all 0.3s;
            }
            
            .tab-btn.active {
                color: #2563eb;
                border-bottom-color: #2563eb;
            }
            
            .tab-btn:hover {
                color: #2563eb;
            }
        `;
        document.head.appendChild(style);

        // التأكد من تحميل قاعدة البيانات
        if (typeof db === 'undefined') {
            console.error('قاعدة البيانات غير محملة - تحقق من ملف supabase.js');
        } else {
            console.log('تم تحميل قاعدة البيانات بنجاح');
        }
    </script>
</body>
</html>
