<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصيانة والتشغيل - نظام إدارة المقاولات</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🏗️ نظام إدارة المقاولات</h2>
                <p>والتطوير العقاري</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="./dashboard.html" class="nav-item">
                    <span class="icon">📊</span>
                    الصفحة الرئيسية
                </a>
                <a href="./projects.html" class="nav-item">
                    <span class="icon">🏗️</span>
                    إدارة المشاريع
                </a>
                <a href="./sales.html" class="nav-item">
                    <span class="icon">🏢</span>
                    مبيعات الشقق
                </a>
                <a href="./contractors.html" class="nav-item">
                    <span class="icon">👷</span>
                    المقاولين والمستخلصات
                </a>
                <a href="./suppliers.html" class="nav-item">
                    <span class="icon">🚚</span>
                    الموردين والفواتير
                </a>
                <a href="./purchases.html" class="nav-item">
                    <span class="icon">🛒</span>
                    المشتريات
                </a>
                <a href="./maintenance.html" class="nav-item active">
                    <span class="icon">🔧</span>
                    الصيانة والتشغيل
                </a>
                <a href="./tasks.html" class="nav-item">
                    <span class="icon">📋</span>
                    المهام اليومية
                </a>
                <a href="./reports.html" class="nav-item">
                    <span class="icon">📊</span>
                    التقارير المالية
                </a>
                <a href="./users.html" class="nav-item">
                    <span class="icon">👥</span>
                    إدارة المستخدمين
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>الصيانة والتشغيل</h1>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">أ</div>
                    <div>
                        <div id="userName">أحمد محمد</div>
                        <div style="font-size: 12px; color: #6b7280;" id="userRole">مدير النظام</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Page Header -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <div>
                        <h2 style="color: #374151; margin-bottom: 5px;">إدارة الصيانة والتشغيل</h2>
                        <p style="color: #6b7280;">إدارة أعمال الصيانة والتشغيل للمشاريع</p>
                    </div>
                    <button class="btn" onclick="openModal()">
                        ➕ مهمة صيانة جديدة
                    </button>
                </div>

                <!-- Stats Row -->
                <div class="stats-row">
                    <div class="mini-stat">
                        <div class="mini-stat-icon">🔧</div>
                        <div class="mini-stat-number">25</div>
                        <div class="mini-stat-label">مهام الصيانة</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">⏳</div>
                        <div class="mini-stat-number">8</div>
                        <div class="mini-stat-label">قيد التنفيذ</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">✅</div>
                        <div class="mini-stat-number">17</div>
                        <div class="mini-stat-label">مكتملة</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">🚨</div>
                        <div class="mini-stat-number">3</div>
                        <div class="mini-stat-label">عاجلة</div>
                    </div>
                </div>

                <!-- Maintenance Tasks Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">مهام الصيانة (<span id="tasksCount">5</span>)</h3>
                    </div>
                    <div class="card-content" style="padding: 0;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المهمة</th>
                                    <th>المشروع</th>
                                    <th>الأولوية</th>
                                    <th>المسؤول</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="tasksBody">
                                <tr>
                                    <td>
                                        <div style="font-weight: bold;">صيانة المصاعد</div>
                                        <div style="font-size: 12px; color: #6b7280;">فحص وصيانة دورية للمصاعد</div>
                                    </td>
                                    <td>مشروع الأندلس السكني</td>
                                    <td><span class="status-badge status-hold">عاجل</span></td>
                                    <td>أحمد محمد</td>
                                    <td>2024-12-25</td>
                                    <td><span class="status-badge status-progress">قيد التنفيذ</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">حذف</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: bold;">تنظيف الخزانات</div>
                                        <div style="font-size: 12px; color: #6b7280;">تنظيف وتعقيم خزانات المياه</div>
                                    </td>
                                    <td>برج النيل التجاري</td>
                                    <td><span class="status-badge status-progress">متوسط</span></td>
                                    <td>فاطمة أحمد</td>
                                    <td>2024-12-28</td>
                                    <td><span class="status-badge status-planning">معلق</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">حذف</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: bold;">فحص الكهرباء</div>
                                        <div style="font-size: 12px; color: #6b7280;">فحص الدوائر الكهربائية</div>
                                    </td>
                                    <td>مجمع الزهراء السكني</td>
                                    <td><span class="status-badge status-completed">منخفض</span></td>
                                    <td>محمد علي</td>
                                    <td>2024-12-30</td>
                                    <td><span class="status-badge status-completed">مكتمل</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">حذف</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: bold;">صيانة التكييف</div>
                                        <div style="font-size: 12px; color: #6b7280;">تنظيف وصيانة أجهزة التكييف</div>
                                    </td>
                                    <td>مشروع الأندلس السكني</td>
                                    <td><span class="status-badge status-progress">متوسط</span></td>
                                    <td>سارة محمد</td>
                                    <td>2025-01-05</td>
                                    <td><span class="status-badge status-planning">معلق</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">حذف</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="font-weight: bold;">طلاء الواجهات</div>
                                        <div style="font-size: 12px; color: #6b7280;">إعادة طلاء الواجهات الخارجية</div>
                                    </td>
                                    <td>برج النيل التجاري</td>
                                    <td><span class="status-badge status-hold">عاجل</span></td>
                                    <td>خالد أحمد</td>
                                    <td>2025-01-10</td>
                                    <td><span class="status-badge status-progress">قيد التنفيذ</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">حذف</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="taskModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>مهمة صيانة جديدة</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="taskForm">
                <div class="form-group">
                    <label for="taskTitle">عنوان المهمة *</label>
                    <input type="text" id="taskTitle" required>
                </div>
                
                <div class="form-group">
                    <label for="taskDescription">الوصف</label>
                    <textarea id="taskDescription" rows="3"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="taskProject">المشروع</label>
                        <select id="taskProject">
                            <option value="">اختر المشروع</option>
                            <option value="1">مشروع الأندلس السكني</option>
                            <option value="2">برج النيل التجاري</option>
                            <option value="3">مجمع الزهراء السكني</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="taskPriority">الأولوية</label>
                        <select id="taskPriority">
                            <option value="low">منخفض</option>
                            <option value="medium">متوسط</option>
                            <option value="high">عالي</option>
                            <option value="urgent">عاجل</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="taskAssigned">المسؤول</label>
                        <input type="text" id="taskAssigned">
                    </div>
                    <div class="form-group">
                        <label for="taskDueDate">تاريخ الاستحقاق</label>
                        <input type="date" id="taskDueDate">
                    </div>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn" style="background: #6b7280;" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <script src="supabase.js"></script>
    <script src="auth.js"></script>
    <script>
        function openModal() {
            document.getElementById('taskModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('taskModal').style.display = 'none';
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('currentUser');
                window.location.href = './index.html';
            }
        }

        // تحقق من تسجيل الدخول
        window.addEventListener('load', function() {
            const currentUser = localStorage.getItem('currentUser');
            if (!currentUser) {
                window.location.href = './index.html';
                return;
            }
            
            const user = JSON.parse(currentUser);
            document.getElementById('userName').textContent = user.name;
            document.getElementById('userAvatar').textContent = user.name.charAt(0);
            
            const roleNames = {
                admin: 'مدير النظام',
                manager: 'مدير',
                accountant: 'محاسب'
            };
            document.getElementById('userRole').textContent = roleNames[user.role] || user.role;
        });

        // إضافة أنماط الإحصائيات
        const style = document.createElement('style');
        style.textContent = `
            .stats-row {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 30px;
            }
            
            .mini-stat {
                background: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            
            .mini-stat-icon {
                font-size: 1.5rem;
                margin-bottom: 10px;
            }
            
            .mini-stat-number {
                font-size: 1.5rem;
                font-weight: bold;
                color: #2563eb;
                margin-bottom: 5px;
            }
            
            .mini-stat-label {
                color: #6b7280;
                font-size: 0.9rem;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
