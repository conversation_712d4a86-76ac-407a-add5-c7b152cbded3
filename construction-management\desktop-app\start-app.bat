@echo off
chcp 65001 >nul
title نظام إدارة المقاولات - تطبيق سطح المكتب

echo.
echo ========================================
echo    نظام إدارة المقاولات والتطوير العقاري
echo         تطبيق سطح المكتب
echo ========================================
echo.

echo 🔧 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo.
    echo 📥 يرجى تحميل وتثبيت Node.js من:
    echo https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js مثبت بنجاح
echo.

echo 📦 التحقق من المكتبات...
if not exist "node_modules" (
    echo 🔄 تثبيت المكتبات المطلوبة...
    echo هذا قد يستغرق بضع دقائق...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات!
        echo.
        echo 💡 جرب تشغيل الملف كمدير (Run as Administrator)
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
    echo.
)

echo 🚀 بدء تشغيل التطبيق...
echo.

npm start

echo.
echo 👋 تم إغلاق التطبيق
pause
