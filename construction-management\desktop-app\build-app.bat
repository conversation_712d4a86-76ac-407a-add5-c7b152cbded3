@echo off
chcp 65001 >nul
title بناء تطبيق سطح المكتب

echo.
echo ========================================
echo    بناء تطبيق نظام إدارة المقاولات
echo ========================================
echo.

echo 🔧 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    pause
    exit /b 1
)

echo ✅ Node.js مثبت بنجاح
echo.

echo 📦 التحقق من المكتبات...
if not exist "node_modules" (
    echo 🔄 تثبيت المكتبات المطلوبة...
    echo هذا قد يستغرق بضع دقائق...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات!
        pause
        exit /b 1
    )
)

echo ✅ المكتبات جاهزة
echo.

echo 🏗️ بناء التطبيق...
echo هذا قد يستغرق 5-10 دقائق...
echo.

npm run build-win

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم بناء التطبيق بنجاح!
    echo.
    echo 📁 ستجد الملفات في مجلد: dist\
    echo.
    echo 📋 الملفات المُنتجة:
    echo    - ملف التثبيت: نظام إدارة المقاولات Setup 1.0.0.exe
    echo    - ملف محمول: نظام إدارة المقاولات 1.0.0.exe
    echo.
    echo 🚀 يمكنك الآن توزيع هذه الملفات!
    echo.
    
    if exist "dist" (
        echo 📂 فتح مجلد النتائج...
        explorer dist
    )
) else (
    echo.
    echo ❌ فشل في بناء التطبيق!
    echo.
    echo 💡 نصائح لحل المشكلة:
    echo    1. تأكد من اتصال الإنترنت
    echo    2. شغل الملف كمدير (Run as Administrator)
    echo    3. تأكد من وجود مساحة كافية على القرص الصلب
    echo.
)

pause
