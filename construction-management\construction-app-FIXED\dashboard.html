<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المقاولات</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>🏗️ نظام إدارة المقاولات</h2>
                <p>والتطوير العقاري</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="./dashboard.html" class="nav-item active">
                    <span class="icon">📊</span>
                    الصفحة الرئيسية
                </a>
                <a href="./projects.html" class="nav-item">
                    <span class="icon">🏗️</span>
                    إدارة المشاريع
                </a>
                <a href="./sales.html" class="nav-item">
                    <span class="icon">🏢</span>
                    مبيعات الشقق
                </a>
                <a href="./contractors.html" class="nav-item">
                    <span class="icon">👷</span>
                    المقاولين والمستخلصات
                </a>
                <a href="./suppliers.html" class="nav-item">
                    <span class="icon">🚚</span>
                    الموردين والفواتير
                </a>
                <a href="./purchases.html" class="nav-item">
                    <span class="icon">🛒</span>
                    المشتريات
                </a>
                <a href="./maintenance.html" class="nav-item">
                    <span class="icon">🔧</span>
                    الصيانة والتشغيل
                </a>
                <a href="./tasks.html" class="nav-item">
                    <span class="icon">📋</span>
                    المهام اليومية
                </a>
                <a href="./reports.html" class="nav-item">
                    <span class="icon">📊</span>
                    التقارير المالية
                </a>
                <a href="./users.html" class="nav-item">
                    <span class="icon">👥</span>
                    إدارة المستخدمين
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div style="display: flex; align-items: center; gap: 15px;">
                    <h1>لوحة التحكم الرئيسية</h1>
                    <div id="connectionStatus" style="display: flex; align-items: center; gap: 5px; padding: 5px 10px; border-radius: 15px; font-size: 12px; font-weight: bold;">
                        <span id="statusIcon">🟡</span>
                        <span id="statusText">جاري التحقق...</span>
                    </div>
                </div>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">أ</div>
                    <div>
                        <div id="userName">أحمد محمد</div>
                        <div style="font-size: 12px; color: #6b7280;" id="userRole">مدير النظام</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">🏗️</div>
                        <div class="stat-number">12</div>
                        <div class="stat-label">إجمالي المشاريع</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🏢</div>
                        <div class="stat-number">85</div>
                        <div class="stat-label">الشقق المباعة</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-number">2.5M</div>
                        <div class="stat-label">إجمالي الإيرادات (ج.م)</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📋</div>
                        <div class="stat-number">23</div>
                        <div class="stat-label">المهام المعلقة</div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">المشاريع الحديثة</h3>
                        </div>
                        <div class="card-content">
                            <div style="margin-bottom: 15px; padding: 15px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-weight: bold; margin-bottom: 5px;">مشروع الأندلس السكني</div>
                                <div style="font-size: 14px; color: #6b7280;">قيد التنفيذ - 75% مكتمل</div>
                            </div>
                            <div style="margin-bottom: 15px; padding: 15px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-weight: bold; margin-bottom: 5px;">برج النيل التجاري</div>
                                <div style="font-size: 14px; color: #6b7280;">في مرحلة التخطيط</div>
                            </div>
                            <div style="padding: 15px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-weight: bold; margin-bottom: 5px;">مجمع الزهراء السكني</div>
                                <div style="font-size: 14px; color: #6b7280;">مكتمل</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">المبيعات الأخيرة</h3>
                        </div>
                        <div class="card-content">
                            <div style="margin-bottom: 15px; padding: 15px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-weight: bold; margin-bottom: 5px;">شقة 201 - مشروع الأندلس</div>
                                <div style="font-size: 14px; color: #6b7280;">أحمد محمد علي - 2,500,000 ج.م</div>
                            </div>
                            <div style="margin-bottom: 15px; padding: 15px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-weight: bold; margin-bottom: 5px;">شقة 105 - برج النيل</div>
                                <div style="font-size: 14px; color: #6b7280;">فاطمة أحمد - 1,800,000 ج.م</div>
                            </div>
                            <div style="padding: 15px; background: #f8fafc; border-radius: 8px;">
                                <div style="font-weight: bold; margin-bottom: 5px;">شقة 302 - مجمع الزهراء</div>
                                <div style="font-size: 14px; color: #6b7280;">محمد حسن - 2,200,000 ج.م</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الإجراءات السريعة</h3>
                    </div>
                    <div class="card-content">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <a href="projects.html" class="btn">إضافة مشروع جديد</a>
                            <a href="sales.html" class="btn btn-success">تسجيل مبيعة جديدة</a>
                            <a href="reports.html" class="btn">عرض التقارير</a>
                            <a href="tasks.html" class="btn">إدارة المهام</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إضافة ملفات الإصلاح المطلوبة -->
    <script src="supabase.js"></script>
    <script src="auth.js"></script>
    <script src="fix-buttons.js"></script>
    <script src="force-online.js"></script>
    <script src="router.js"></script>
    <script>
        // تحقق من تسجيل الدخول
        function checkAuth() {
            const currentUser = localStorage.getItem('currentUser');
            if (!currentUser) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(currentUser);
        }

        // تحديث معلومات المستخدم
        function updateUserInfo() {
            const user = checkAuth();
            if (user) {
                document.getElementById('userName').textContent = user.name;
                document.getElementById('userAvatar').textContent = user.name.charAt(0);
                
                const roleNames = {
                    admin: 'مدير النظام',
                    manager: 'مدير',
                    accountant: 'محاسب'
                };
                document.getElementById('userRole').textContent = roleNames[user.role] || user.role;
            }
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('currentUser');
                window.location.href = 'index.html';
            }
        }

        // تحديث الإحصائيات (محاكاة)
        function updateStats() {
            // يمكن ربطها بقاعدة بيانات حقيقية لاحقاً
            const stats = [
                { projects: 12, sales: 85, revenue: '2.5M', tasks: 23 }
            ];
        }

        // فحص حالة الاتصال
        async function checkConnectionStatus() {
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            const statusDiv = document.getElementById('connectionStatus');

            try {
                if (db.isOnline) {
                    // اختبار الاتصال مع Supabase
                    const testResult = await db.getData('projects');
                    if (testResult.success) {
                        statusIcon.textContent = '🟢';
                        statusText.textContent = 'متصل';
                        statusDiv.style.background = '#dcfce7';
                        statusDiv.style.color = '#166534';
                    } else {
                        throw new Error('فشل الاتصال');
                    }
                } else {
                    throw new Error('غير متصل');
                }
            } catch (error) {
                statusIcon.textContent = '🟡';
                statusText.textContent = 'محلي';
                statusDiv.style.background = '#fef3c7';
                statusDiv.style.color = '#92400e';
            }
        }

        // تحديث الإحصائيات من قاعدة البيانات
        async function updateStatsFromDB() {
            try {
                const [projectsResult, salesResult] = await Promise.all([
                    db.getData('projects'),
                    db.getData('sales')
                ]);

                if (projectsResult.success && salesResult.success) {
                    const projects = projectsResult.data || [];
                    const sales = salesResult.data || [];

                    // تحديث الإحصائيات
                    const statsCards = document.querySelectorAll('.stat-number');
                    if (statsCards[0]) statsCards[0].textContent = projects.length;
                    if (statsCards[1]) statsCards[1].textContent = sales.length;

                    // حساب إجمالي الإيرادات
                    const totalRevenue = sales.reduce((sum, sale) => sum + (sale.sale_price || 0), 0);
                    if (statsCards[2]) statsCards[2].textContent = (totalRevenue / 1000000).toFixed(1) + 'M';
                }
            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        // تهيئة الصفحة
        window.addEventListener('load', function() {
            updateUserInfo();
            checkConnectionStatus();
            updateStatsFromDB();

            // فحص حالة الاتصال كل 30 ثانية
            setInterval(checkConnectionStatus, 30000);
        });

        // التأكد من تحميل قاعدة البيانات
        if (typeof db === 'undefined') {
            console.error('قاعدة البيانات غير محملة - تحقق من ملف supabase.js');
        } else {
            console.log('تم تحميل قاعدة البيانات بنجاح');
        }
    </script>
</body>
</html>
