<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مبيعات الشقق - نظام إدارة المقاولات</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .mini-stat {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .mini-stat-icon {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .mini-stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .mini-stat-label {
            color: #6b7280;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🏗️ نظام إدارة المقاولات</h2>
                <p>والتطوير العقاري</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.html" class="nav-item">
                    <span class="icon">📊</span>
                    الصفحة الرئيسية
                </a>
                <a href="projects.html" class="nav-item">
                    <span class="icon">🏗️</span>
                    إدارة المشاريع
                </a>
                <a href="sales.html" class="nav-item active">
                    <span class="icon">🏢</span>
                    مبيعات الشقق
                </a>
                <a href="contractors.html" class="nav-item">
                    <span class="icon">👷</span>
                    المقاولين والمستخلصات
                </a>
                <a href="suppliers.html" class="nav-item">
                    <span class="icon">🚚</span>
                    الموردين والفواتير
                </a>
                <a href="purchases.html" class="nav-item">
                    <span class="icon">🛒</span>
                    المشتريات
                </a>
                <a href="maintenance.html" class="nav-item">
                    <span class="icon">🔧</span>
                    الصيانة والتشغيل
                </a>
                <a href="tasks.html" class="nav-item">
                    <span class="icon">📋</span>
                    المهام اليومية
                </a>
                <a href="reports.html" class="nav-item">
                    <span class="icon">📊</span>
                    التقارير المالية
                </a>
                <a href="users.html" class="nav-item">
                    <span class="icon">👥</span>
                    إدارة المستخدمين
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>مبيعات الشقق</h1>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">أ</div>
                    <div>
                        <div id="userName">أحمد محمد</div>
                        <div style="font-size: 12px; color: #6b7280;" id="userRole">مدير النظام</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Page Header -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <div>
                        <h2 style="color: #374151; margin-bottom: 5px;">إدارة مبيعات الشقق</h2>
                        <p style="color: #6b7280;">إدارة مبيعات الشقق وتتبع العملاء</p>
                    </div>
                    <button class="btn" onclick="openModal()">
                        ➕ مبيعة جديدة
                    </button>
                </div>

                <!-- Stats Row -->
                <div class="stats-row">
                    <div class="mini-stat">
                        <div class="mini-stat-icon">✅</div>
                        <div class="mini-stat-number" id="completedSales">12</div>
                        <div class="mini-stat-label">المبيعات المكتملة</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">⏳</div>
                        <div class="mini-stat-number" id="pendingSales">5</div>
                        <div class="mini-stat-label">المبيعات المعلقة</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">💰</div>
                        <div class="mini-stat-number" id="totalRevenue">25.5M</div>
                        <div class="mini-stat-label">إجمالي الإيرادات</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">📊</div>
                        <div class="mini-stat-number" id="avgPrice">2.1M</div>
                        <div class="mini-stat-label">متوسط سعر البيع</div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card" style="margin-bottom: 20px;">
                    <div class="card-content" style="padding: 20px;">
                        <div class="search-bar">
                            <input type="text" class="search-input" placeholder="البحث في المبيعات..." id="searchInput">
                            <select class="filter-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="pending">معلق</option>
                                <option value="completed">مكتمل</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                            <button class="btn" onclick="filterSales()">بحث</button>
                        </div>
                    </div>
                </div>

                <!-- Sales Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">قائمة المبيعات (<span id="salesCount">5</span>)</h3>
                    </div>
                    <div class="card-content" style="padding: 0;">
                        <table class="table" id="salesTable">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>الشقة</th>
                                    <th>المشروع</th>
                                    <th>سعر البيع</th>
                                    <th>المقدم</th>
                                    <th>تاريخ البيع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="salesBody">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="saleModal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 id="modalTitle">مبيعة جديدة</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="saleForm">
                <div class="form-group">
                    <label for="apartmentSelect">الشقة *</label>
                    <select id="apartmentSelect" required>
                        <option value="">اختر الشقة</option>
                        <option value="1">شقة 101 - مشروع الأندلس - 2,500,000 ج.م</option>
                        <option value="2">شقة 201 - مشروع الأندلس - 2,800,000 ج.م</option>
                        <option value="3">شقة 301 - برج النيل - 3,200,000 ج.م</option>
                    </select>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerName">اسم العميل *</label>
                        <input type="text" id="customerName" required>
                    </div>
                    <div class="form-group">
                        <label for="customerPhone">رقم الهاتف</label>
                        <input type="tel" id="customerPhone">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="customerEmail">البريد الإلكتروني</label>
                    <input type="email" id="customerEmail">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="salePrice">سعر البيع</label>
                        <input type="number" id="salePrice" placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label for="downPayment">المقدم</label>
                        <input type="number" id="downPayment" placeholder="0.00">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="installmentPlan">خطة التقسيط</label>
                    <input type="text" id="installmentPlan" placeholder="مثال: 24 شهر">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="saleDate">تاريخ البيع</label>
                        <input type="date" id="saleDate">
                    </div>
                    <div class="form-group">
                        <label for="saleStatus">الحالة</label>
                        <select id="saleStatus">
                            <option value="pending">معلق</option>
                            <option value="completed">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                    </div>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn" style="background: #6b7280;" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- إضافة ملفات الإصلاح المطلوبة -->
    <script src="fix-everything.js"></script>
    <script>
        // بيانات المبيعات (محاكاة قاعدة البيانات)
        let sales = [
            {
                id: 1,
                customerName: 'أحمد محمد علي',
                customerPhone: '01234567890',
                customerEmail: '<EMAIL>',
                apartmentNumber: '201',
                projectName: 'مشروع الأندلس',
                salePrice: 2500000,
                downPayment: 500000,
                installmentPlan: '24 شهر',
                saleDate: '2024-12-20',
                status: 'completed'
            },
            {
                id: 2,
                customerName: 'فاطمة أحمد',
                customerPhone: '01098765432',
                customerEmail: '<EMAIL>',
                apartmentNumber: '105',
                projectName: 'برج النيل',
                salePrice: 1800000,
                downPayment: 300000,
                installmentPlan: '36 شهر',
                saleDate: '2024-12-19',
                status: 'pending'
            },
            {
                id: 3,
                customerName: 'محمد حسن',
                customerPhone: '01555666777',
                customerEmail: '<EMAIL>',
                apartmentNumber: '302',
                projectName: 'مجمع الزهراء',
                salePrice: 2200000,
                downPayment: 400000,
                installmentPlan: '30 شهر',
                saleDate: '2024-12-18',
                status: 'completed'
            },
            {
                id: 4,
                customerName: 'سارة علي',
                customerPhone: '01777888999',
                customerEmail: '<EMAIL>',
                apartmentNumber: '401',
                projectName: 'مشروع الأندلس',
                salePrice: 2800000,
                downPayment: 600000,
                installmentPlan: '18 شهر',
                saleDate: '2024-12-17',
                status: 'pending'
            },
            {
                id: 5,
                customerName: 'خالد محمود',
                customerPhone: '01444333222',
                customerEmail: '<EMAIL>',
                apartmentNumber: '501',
                projectName: 'برج النيل',
                salePrice: 3200000,
                downPayment: 800000,
                installmentPlan: '20 شهر',
                saleDate: '2024-12-16',
                status: 'completed'
            }
        ];

        let editingSaleId = null;

        // عرض المبيعات
        function displaySales(salesToShow = sales) {
            const tbody = document.getElementById('salesBody');
            tbody.innerHTML = '';

            salesToShow.forEach(sale => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: bold;">${sale.customerName}</div>
                        <div style="font-size: 12px; color: #6b7280;">${sale.customerPhone}</div>
                    </td>
                    <td>شقة ${sale.apartmentNumber}</td>
                    <td>${sale.projectName}</td>
                    <td>${formatCurrency(sale.salePrice)}</td>
                    <td>${formatCurrency(sale.downPayment)}</td>
                    <td>${formatDate(sale.saleDate)}</td>
                    <td>${getStatusBadge(sale.status)}</td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;" onclick="editSale(${sale.id})">تعديل</button>
                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteSale(${sale.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('salesCount').textContent = salesToShow.length;
            updateStats();
        }

        // تحديث الإحصائيات
        function updateStats() {
            const completedSales = sales.filter(s => s.status === 'completed');
            const pendingSales = sales.filter(s => s.status === 'pending');
            const totalRevenue = completedSales.reduce((sum, s) => sum + s.salePrice, 0);
            const avgPrice = completedSales.length > 0 ? totalRevenue / completedSales.length : 0;

            document.getElementById('completedSales').textContent = completedSales.length;
            document.getElementById('pendingSales').textContent = pendingSales.length;
            document.getElementById('totalRevenue').textContent = formatCurrencyShort(totalRevenue);
            document.getElementById('avgPrice').textContent = formatCurrencyShort(avgPrice);
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-EG').format(amount) + ' ج.م';
        }

        // تنسيق العملة مختصر
        function formatCurrencyShort(amount) {
            if (amount >= 1000000) {
                return (amount / 1000000).toFixed(1) + 'M';
            } else if (amount >= 1000) {
                return (amount / 1000).toFixed(1) + 'K';
            }
            return amount.toString();
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-EG');
        }

        // شارة الحالة
        function getStatusBadge(status) {
            const statusMap = {
                pending: { label: 'معلق', class: 'status-planning' },
                completed: { label: 'مكتمل', class: 'status-completed' },
                cancelled: { label: 'ملغي', class: 'status-hold' }
            };
            
            const statusInfo = statusMap[status] || { label: status, class: 'status-planning' };
            return `<span class="status-badge ${statusInfo.class}">${statusInfo.label}</span>`;
        }

        // فتح النافذة المنبثقة
        function openModal() {
            document.getElementById('saleModal').style.display = 'block';
            document.getElementById('modalTitle').textContent = 'مبيعة جديدة';
            document.getElementById('saleForm').reset();
            editingSaleId = null;
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('saleModal').style.display = 'none';
        }

        // تعديل مبيعة
        function editSale(id) {
            const sale = sales.find(s => s.id === id);
            if (sale) {
                editingSaleId = id;
                document.getElementById('modalTitle').textContent = 'تعديل المبيعة';
                document.getElementById('customerName').value = sale.customerName;
                document.getElementById('customerPhone').value = sale.customerPhone || '';
                document.getElementById('customerEmail').value = sale.customerEmail || '';
                document.getElementById('salePrice').value = sale.salePrice || '';
                document.getElementById('downPayment').value = sale.downPayment || '';
                document.getElementById('installmentPlan').value = sale.installmentPlan || '';
                document.getElementById('saleDate').value = sale.saleDate || '';
                document.getElementById('saleStatus').value = sale.status;
                document.getElementById('saleModal').style.display = 'block';
            }
        }

        // حذف مبيعة
        function deleteSale(id) {
            if (confirm('هل أنت متأكد من حذف هذه المبيعة؟')) {
                sales = sales.filter(s => s.id !== id);
                displaySales();
            }
        }

        // تصفية المبيعات
        function filterSales() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            let filtered = sales;

            if (searchTerm) {
                filtered = filtered.filter(sale =>
                    sale.customerName.toLowerCase().includes(searchTerm) ||
                    sale.customerPhone.toLowerCase().includes(searchTerm) ||
                    sale.apartmentNumber.toLowerCase().includes(searchTerm)
                );
            }

            if (statusFilter) {
                filtered = filtered.filter(sale => sale.status === statusFilter);
            }

            displaySales(filtered);
        }

        // حفظ المبيعة
        document.getElementById('saleForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const saleData = {
                customerName: document.getElementById('customerName').value,
                customerPhone: document.getElementById('customerPhone').value,
                customerEmail: document.getElementById('customerEmail').value,
                apartmentNumber: '101', // يمكن ربطها بالشقة المختارة
                projectName: 'مشروع الأندلس', // يمكن ربطها بالمشروع
                salePrice: parseFloat(document.getElementById('salePrice').value) || 0,
                downPayment: parseFloat(document.getElementById('downPayment').value) || 0,
                installmentPlan: document.getElementById('installmentPlan').value,
                saleDate: document.getElementById('saleDate').value,
                status: document.getElementById('saleStatus').value
            };

            try {
                let result;
                if (editingSaleId) {
                    // تعديل مبيعة موجودة
                    result = await db.updateData('sales', editingSaleId, saleData);
                    if (result.success) {
                        const index = sales.findIndex(s => s.id === editingSaleId);
                        if (index !== -1) {
                            sales[index] = { ...sales[index], ...saleData };
                        }
                        alert('تم تحديث المبيعة بنجاح!');
                    }
                } else {
                    // إضافة مبيعة جديدة
                    result = await db.insertData('sales', saleData);
                    if (result.success) {
                        sales.push(result.data);
                        alert('تم إضافة المبيعة بنجاح!');
                    }
                }

                if (result.success) {
                    displaySales();
                    closeModal();
                    document.getElementById('saleForm').reset();
                    editingSaleId = null;
                } else {
                    alert('حدث خطأ: ' + result.error);
                }
            } catch (error) {
                console.error('خطأ في حفظ المبيعة:', error);
                alert('حدث خطأ في حفظ المبيعة');
            }
        });

        // تهيئة الصفحة
        window.addEventListener('load', function() {
            checkAuthAndUpdateUI();
            displaySales();

            // إضافة مستمع للبحث المباشر
            document.getElementById('searchInput').addEventListener('input', filterSales);
            document.getElementById('statusFilter').addEventListener('change', filterSales);
        });

        // إغلاق النافذة عند النقر خارجها
        window.addEventListener('click', function(e) {
            const modal = document.getElementById('saleModal');
            if (e.target === modal) {
                closeModal();
            }
        });

        // التأكد من تحميل قاعدة البيانات
        if (typeof db === 'undefined') {
            console.error('قاعدة البيانات غير محملة - تحقق من ملف supabase.js');
        } else {
            console.log('تم تحميل قاعدة البيانات بنجاح');
        }
    </script>
</body>
</html>
