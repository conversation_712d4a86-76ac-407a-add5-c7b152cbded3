// اختبار الاتصال بقاعدة البيانات Supabase
console.log('🔧 تحميل اختبار الاتصال...');

// انتظار تحميل Supabase
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(testSupabaseConnection, 2000);
});

// إذا كانت الصفحة محملة بالفعل
if (document.readyState === 'complete') {
    setTimeout(testSupabaseConnection, 2000);
}

async function testSupabaseConnection() {
    console.log('🔄 بدء اختبار الاتصال بـ Supabase...');
    
    // التحقق من وجود متغيرات Supabase
    if (typeof SUPABASE_URL === 'undefined') {
        console.error('❌ SUPABASE_URL غير معرف');
        showConnectionStatus('محلي', 'SUPABASE_URL غير معرف');
        return;
    }
    
    if (typeof SUPABASE_ANON_KEY === 'undefined') {
        console.error('❌ SUPABASE_ANON_KEY غير معرف');
        showConnectionStatus('محلي', 'SUPABASE_ANON_KEY غير معرف');
        return;
    }
    
    console.log('✅ متغيرات Supabase معرفة بشكل صحيح');
    console.log('🔗 URL:', SUPABASE_URL);
    console.log('🔑 Key:', SUPABASE_ANON_KEY ? 'موجود' : 'غير موجود');
    
    // التحقق من وجود عميل Supabase
    if (typeof supabase === 'undefined') {
        console.error('❌ عميل Supabase غير محمل');
        showConnectionStatus('محلي', 'عميل Supabase غير محمل');
        return;
    }
    
    console.log('✅ عميل Supabase محمل بشكل صحيح');
    
    try {
        // اختبار الاتصال الأساسي
        console.log('🔄 اختبار الاتصال بجدول المشاريع...');
        
        const { data, error } = await supabase
            .from('projects')
            .select('id, name')
            .limit(1);
        
        if (error) {
            console.error('❌ خطأ في الاتصال:', error.message);
            console.error('📋 تفاصيل الخطأ:', error);
            
            // رسائل خطأ مفصلة
            if (error.message.includes('relation "projects" does not exist')) {
                console.error('🗄️ جدول المشاريع غير موجود');
                console.error('💡 الحل: شغل SQL Schema في Supabase Dashboard');
                showConnectionStatus('محلي', 'جدول المشاريع غير موجود - شغل SQL Schema');
            } else if (error.message.includes('Invalid API key')) {
                console.error('🔑 مفتاح API غير صحيح');
                showConnectionStatus('محلي', 'مفتاح API غير صحيح');
            } else {
                showConnectionStatus('محلي', error.message);
            }
            
            return false;
        }
        
        console.log('✅ تم الاتصال بقاعدة البيانات بنجاح!');
        console.log('📊 البيانات المستلمة:', data);
        
        // اختبار إضافي - عدد المشاريع
        const { count, error: countError } = await supabase
            .from('projects')
            .select('*', { count: 'exact', head: true });
            
        if (!countError) {
            console.log(`📈 عدد المشاريع في قاعدة البيانات: ${count}`);
        }
        
        showConnectionStatus('متصل', `متصل بنجاح - ${count || 0} مشروع`);
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في اختبار الاتصال:', error);
        showConnectionStatus('محلي', 'خطأ في الشبكة أو الإعدادات');
        return false;
    }
}

function showConnectionStatus(status, details) {
    // تحديث حالة الاتصال في لوحة التحكم
    const statusElements = document.querySelectorAll('.connection-status');
    statusElements.forEach(element => {
        if (status === 'متصل') {
            element.innerHTML = '🟢 متصل';
            element.className = 'connection-status connected';
        } else {
            element.innerHTML = '🟡 محلي';
            element.className = 'connection-status local';
        }
    });
    
    // إظهار تفاصيل في Console
    console.log(`📊 حالة الاتصال: ${status}`);
    if (details) {
        console.log(`📋 التفاصيل: ${details}`);
    }
    
    // إظهار إشعار للمستخدم
    if (status === 'محلي' && details) {
        setTimeout(() => {
            if (confirm(`تحذير: التطبيق يعمل بالوضع المحلي\n\nالسبب: ${details}\n\nهل تريد إعادة المحاولة؟`)) {
                location.reload();
            }
        }, 3000);
    }
}

// دالة لإعادة اختبار الاتصال
window.retestConnection = function() {
    console.log('🔄 إعادة اختبار الاتصال...');
    testSupabaseConnection();
};

// دالة لعرض معلومات التشخيص
window.showDiagnostics = function() {
    console.log('🔍 معلومات التشخيص:');
    console.log('🔗 SUPABASE_URL:', typeof SUPABASE_URL !== 'undefined' ? SUPABASE_URL : 'غير معرف');
    console.log('🔑 SUPABASE_ANON_KEY:', typeof SUPABASE_ANON_KEY !== 'undefined' ? 'موجود' : 'غير موجود');
    console.log('🌐 supabase client:', typeof supabase !== 'undefined' ? 'محمل' : 'غير محمل');
    console.log('🗄️ db object:', typeof db !== 'undefined' ? 'موجود' : 'غير موجود');
    
    // اختبار الشبكة
    fetch(SUPABASE_URL + '/rest/v1/')
        .then(response => {
            console.log('🌐 اختبار الشبكة: نجح - Status:', response.status);
        })
        .catch(error => {
            console.error('🌐 اختبار الشبكة: فشل -', error);
        });
};

console.log('✅ تم تحميل اختبار الاتصال');
console.log('💡 للاختبار اليدوي: retestConnection()');
console.log('💡 لمعلومات التشخيص: showDiagnostics()');
