// إجبار التطبيق على الاتصال بـ Supabase
console.log('🔧 تحميل إجبار الاتصال بـ Supabase...');

// إعادة تعريف إعدادات Supabase
window.SUPABASE_URL = 'https://xlpcpojmatiejxizukkz.supabase.co';
window.SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhscGNwb2ptYXRpZWp4aXp1a2t6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NDg0NzksImV4cCI6MjA2NjUyNDQ3OX0.eDNwBdyHfuQLeYqX_LVHdUekyHVmA3m9PH2yu_RV9Vc';

// تحميل مكتبة Supabase من CDN
if (typeof supabase === 'undefined') {
    console.log('📦 تحميل مكتبة Supabase من CDN...');
    
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';
    script.onload = function() {
        console.log('✅ تم تحميل مكتبة Supabase');
        initializeSupabase();
    };
    document.head.appendChild(script);
} else {
    initializeSupabase();
}

function initializeSupabase() {
    console.log('🔧 إعداد عميل Supabase...');
    
    // إنشاء عميل Supabase
    window.supabase = window.supabase.createClient(
        window.SUPABASE_URL,
        window.SUPABASE_ANON_KEY
    );
    
    console.log('✅ تم إنشاء عميل Supabase');
    
    // إعادة تعريف كائن قاعدة البيانات
    window.db = {
        isOnline: true,
        
        // دالة للحصول على البيانات
        async get(table) {
            try {
                const { data, error } = await window.supabase
                    .from(table)
                    .select('*')
                    .order('created_at', { ascending: false });
                
                if (error) {
                    console.error(`❌ خطأ في جلب بيانات ${table}:`, error);
                    return [];
                }
                
                console.log(`✅ تم جلب ${data.length} عنصر من ${table}`);
                return data;
            } catch (error) {
                console.error(`❌ خطأ في الاتصال بـ ${table}:`, error);
                return [];
            }
        },
        
        // دالة لإضافة البيانات
        async add(table, data) {
            try {
                const { data: result, error } = await window.supabase
                    .from(table)
                    .insert([data])
                    .select();
                
                if (error) {
                    console.error(`❌ خطأ في إضافة بيانات إلى ${table}:`, error);
                    return null;
                }
                
                console.log(`✅ تم إضافة بيانات إلى ${table}:`, result[0]);
                return result[0];
            } catch (error) {
                console.error(`❌ خطأ في إضافة البيانات:`, error);
                return null;
            }
        },
        
        // دالة لتحديث البيانات
        async update(table, id, data) {
            try {
                const { data: result, error } = await window.supabase
                    .from(table)
                    .update(data)
                    .eq('id', id)
                    .select();
                
                if (error) {
                    console.error(`❌ خطأ في تحديث بيانات ${table}:`, error);
                    return null;
                }
                
                console.log(`✅ تم تحديث بيانات ${table}:`, result[0]);
                return result[0];
            } catch (error) {
                console.error(`❌ خطأ في تحديث البيانات:`, error);
                return null;
            }
        },
        
        // دالة لحذف البيانات
        async delete(table, id) {
            try {
                const { error } = await window.supabase
                    .from(table)
                    .delete()
                    .eq('id', id);
                
                if (error) {
                    console.error(`❌ خطأ في حذف بيانات من ${table}:`, error);
                    return false;
                }
                
                console.log(`✅ تم حذف البيانات من ${table}`);
                return true;
            } catch (error) {
                console.error(`❌ خطأ في حذف البيانات:`, error);
                return false;
            }
        }
    };
    
    // اختبار الاتصال
    testConnection();
    
    // تحديث حالة الاتصال
    updateConnectionStatus();
}

async function testConnection() {
    console.log('🔄 اختبار الاتصال...');
    
    try {
        // محاولة جلب مشروع واحد
        const projects = await window.db.get('projects');
        
        if (projects && projects.length >= 0) {
            console.log('✅ الاتصال ناجح!');
            console.log(`📊 عدد المشاريع: ${projects.length}`);
            
            // إذا لم توجد مشاريع، أضف مشروع تجريبي
            if (projects.length === 0) {
                console.log('📝 إضافة مشروع تجريبي...');
                await window.db.add('projects', {
                    name: 'مشروع تجريبي',
                    description: 'مشروع للاختبار',
                    location: 'القاهرة',
                    status: 'planning',
                    budget: 1000000
                });
            }
            
            return true;
        }
    } catch (error) {
        console.error('❌ فشل اختبار الاتصال:', error);
        
        // محاولة إنشاء الجداول تلقائياً
        await createTablesIfNeeded();
    }
    
    return false;
}

async function createTablesIfNeeded() {
    console.log('🔧 محاولة إنشاء الجداول...');
    
    try {
        // محاولة إنشاء جدول المشاريع
        const { error } = await window.supabase.rpc('create_projects_table', {});
        
        if (!error) {
            console.log('✅ تم إنشاء الجداول');
            // إعادة اختبار الاتصال
            setTimeout(testConnection, 2000);
        }
    } catch (error) {
        console.log('ℹ️ لم يتم إنشاء الجداول تلقائياً - يرجى إنشاؤها يدوياً');
    }
}

function updateConnectionStatus() {
    // تحديث جميع عناصر حالة الاتصال
    const statusElements = document.querySelectorAll('.connection-status');
    statusElements.forEach(element => {
        element.innerHTML = '🟢 متصل';
        element.className = 'connection-status connected';
    });
    
    // تحديث النص في لوحة التحكم
    const dashboardStatus = document.querySelector('.status-text');
    if (dashboardStatus) {
        dashboardStatus.textContent = 'متصل';
    }
    
    console.log('✅ تم تحديث حالة الاتصال إلى "متصل"');
}

// تشغيل التحديث كل 5 ثوان
setInterval(updateConnectionStatus, 5000);

// دالة لإعادة تحميل البيانات
window.reloadData = async function() {
    console.log('🔄 إعادة تحميل البيانات...');
    
    // إعادة تحميل المشاريع
    if (typeof loadProjects === 'function') {
        await loadProjects();
    }
    
    // إعادة تحميل المبيعات
    if (typeof loadSales === 'function') {
        await loadSales();
    }
    
    // إعادة تحميل المقاولين
    if (typeof loadContractors === 'function') {
        await loadContractors();
    }
    
    // إعادة تحميل الموردين
    if (typeof loadSuppliers === 'function') {
        await loadSuppliers();
    }
    
    // إعادة تحميل المشتريات
    if (typeof loadPurchases === 'function') {
        await loadPurchases();
    }
    
    console.log('✅ تم إعادة تحميل جميع البيانات');
};

console.log('✅ تم تحميل إجبار الاتصال بـ Supabase');
console.log('💡 لإعادة تحميل البيانات: reloadData()');
