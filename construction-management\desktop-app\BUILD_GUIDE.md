# 🖥️ دليل بناء تطبيق سطح المكتب

## 📦 **تحويل التطبيق إلى ملف تنفيذي**

تم إعداد كل شيء لتحويل التطبيق إلى ملف تنفيذي يعمل على أجهزة المكتب.

---

## 🛠️ **المتطلبات:**

### **تثبيت Node.js:**
1. **حمل Node.js من:**
   ```
   https://nodejs.org/
   ```
2. **اختر LTS Version (الإصدار المستقر)**
3. **ثبت البرنامج بالإعدادات الافتراضية**

---

## 🚀 **خطوات البناء:**

### **الخطوة 1: تثبيت المكتبات**

افتح Command Prompt أو PowerShell في مجلد `desktop-app`:

```bash
cd "construction-management\desktop-app"
npm install
```

### **الخطوة 2: بناء التطبيق**

#### **لنظام Windows:**
```bash
npm run build-win
```

#### **لجميع الأنظمة:**
```bash
npm run build
```

#### **للاختبار فقط (بدون تثبيت):**
```bash
npm run pack
```

---

## 📁 **النتائج:**

### **بعد البناء ستجد:**

```
desktop-app/
├── dist/
│   ├── نظام إدارة المقاولات Setup 1.0.0.exe  (ملف التثبيت)
│   ├── نظام إدارة المقاولات 1.0.0.exe        (الملف التنفيذي)
│   └── win-unpacked/                          (مجلد التطبيق)
```

---

## 🎯 **أنواع الملفات المُنتجة:**

### **1. ملف التثبيت (.exe):**
- **الاسم:** `نظام إدارة المقاولات Setup 1.0.0.exe`
- **الحجم:** ~150-200 MB
- **الاستخدام:** للتوزيع والتثبيت على أجهزة أخرى
- **المميزات:**
  - ✅ تثبيت تلقائي
  - ✅ إنشاء اختصارات
  - ✅ إضافة للبرامج المثبتة
  - ✅ إلغاء تثبيت سهل

### **2. الملف التنفيذي المحمول:**
- **الاسم:** `نظام إدارة المقاولات 1.0.0.exe`
- **الحجم:** ~100-150 MB
- **الاستخدام:** تشغيل مباشر بدون تثبيت
- **المميزات:**
  - ✅ لا يحتاج تثبيت
  - ✅ يمكن نسخه على فلاشة
  - ✅ يعمل من أي مكان

### **3. مجلد التطبيق (win-unpacked):**
- **المحتوى:** جميع ملفات التطبيق
- **الاستخدام:** للتطوير والاختبار
- **التشغيل:** تشغيل `نظام إدارة المقاولات.exe` من داخل المجلد

---

## 🌐 **مشاركة التطبيق في الشبكة:**

### **الطريقة الأولى: ملف التثبيت**
1. **انسخ ملف:** `نظام إدارة المقاولات Setup 1.0.0.exe`
2. **شاركه عبر:**
   - الشبكة المحلية
   - البريد الإلكتروني
   - فلاشة USB
   - خدمات التخزين السحابي

### **الطريقة الثانية: الملف المحمول**
1. **انسخ ملف:** `نظام إدارة المقاولات 1.0.0.exe`
2. **ضعه في مجلد مشترك على الشبكة**
3. **شغله مباشرة من الشبكة**

### **الطريقة الثالثة: مجلد كامل**
1. **انسخ مجلد:** `win-unpacked`
2. **شاركه على الشبكة**
3. **شغل الملف التنفيذي من داخل المجلد**

---

## 🔧 **إعدادات متقدمة:**

### **تخصيص التطبيق:**

#### **تغيير الأيقونة:**
1. **ضع ملف أيقونة (.ico) في:** `assets/icon.ico`
2. **أعد البناء**

#### **تغيير اسم التطبيق:**
1. **عدل في:** `package.json`
   ```json
   "productName": "اسم التطبيق الجديد"
   ```

#### **تغيير الإصدار:**
1. **عدل في:** `package.json`
   ```json
   "version": "2.0.0"
   ```

---

## 🖥️ **متطلبات التشغيل:**

### **الحد الأدنى:**
- **النظام:** Windows 7/8/10/11
- **المعالج:** Intel/AMD x64
- **الذاكرة:** 2 GB RAM
- **المساحة:** 500 MB
- **الإنترنت:** مطلوب للاتصال بقاعدة البيانات

### **الموصى به:**
- **النظام:** Windows 10/11
- **المعالج:** Intel i3 أو أفضل
- **الذاكرة:** 4 GB RAM
- **المساحة:** 1 GB
- **الإنترنت:** اتصال مستقر

---

## 🚀 **اختبار التطبيق:**

### **قبل التوزيع:**
1. **شغل التطبيق على جهازك**
2. **اختبر جميع الوظائف:**
   - تسجيل الدخول
   - إضافة البيانات
   - حفظ البيانات
   - الطباعة والتصدير

3. **اختبر على جهاز آخر:**
   - ثبت التطبيق
   - تأكد من عمل الاتصال بالإنترنت
   - اختبر جميع الوظائف

---

## 📋 **مميزات تطبيق سطح المكتب:**

### **مقارنة مع التطبيق الويب:**

| الميزة | تطبيق الويب | تطبيق سطح المكتب |
|--------|-------------|------------------|
| **التثبيت** | لا يحتاج | يحتاج تثبيت |
| **الأداء** | يعتمد على المتصفح | أسرع وأكثر استقراراً |
| **الوصول** | يحتاج إنترنت | يعمل محلياً |
| **التحديثات** | تلقائية | يدوية |
| **الأمان** | أقل | أكثر أماناً |
| **التخصيص** | محدود | كامل |

---

## 🎉 **النتيجة النهائية:**

**تطبيق سطح مكتب كامل يحتوي على:**
- 🖥️ **واجهة مستخدم احترافية**
- 🗄️ **اتصال بقاعدة البيانات**
- 📊 **جميع وظائف إدارة المقاولات**
- 🔐 **نظام مصادقة آمن**
- 📱 **متوافق مع جميع أحجام الشاشات**
- 🌐 **يعمل مع أو بدون إنترنت**

---

## 📞 **الدعم:**

### **إذا واجهت مشاكل:**
1. **تأكد من تثبيت Node.js**
2. **شغل الأوامر كمدير (Run as Administrator)**
3. **تحقق من اتصال الإنترنت**
4. **أعد تشغيل الجهاز بعد التثبيت**

---

**🚀 ابدأ البناء الآن واحصل على تطبيق سطح مكتب احترافي!**
