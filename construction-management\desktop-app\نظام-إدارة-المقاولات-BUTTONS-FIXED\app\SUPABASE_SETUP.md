# 🗄️ دليل إعداد Supabase - خطوة بخطوة

## لربط النظام بقاعدة بيانات حقيقية

### 🎯 **نظرة عامة**

النظام يعمل حالياً في **الوضع المحلي** باستخدام localStorage. لربطه بقاعدة بيانات حقيقية، اتبع هذا الدليل.

---

## الخطوة 1: إنشاء مشروع Supabase (5 دقائق) 🚀

### 1.1 إنشاء حساب
1. اذهب إلى [supabase.com](https://supabase.com)
2. اضغط **"Start your project"**
3. سجل دخول بـ GitHub أو أنشئ حساب جديد

### 1.2 إنشاء مشروع جديد
1. اضغط **"New Project"**
2. اختر Organization أو أنشئ واحد جديد
3. املأ بيانات المشروع:
   ```
   Project name: construction-management
   Database Password: [كلمة مرور قوية - احفظها!]
   Region: [اختر أقرب منطقة]
   ```
4. اضغط **"Create new project"**
5. انتظر 2-3 دقائق حتى يكتمل الإعداد

---

## الخطوة 2: إعداد قاعدة البيانات (3 دقائق) 🗄️

### 2.1 تشغيل Database Schema
1. في Supabase Dashboard، اذهب إلى **SQL Editor**
2. اضغط **"New query"**
3. انسخ والصق محتوى ملف `../database/schema_fixed.sql` بالكامل
4. اضغط **"Run"** لتشغيل الـ SQL
5. تأكد من ظهور رسالة **"Success"** خضراء

### 2.2 التحقق من الجداول
1. اذهب إلى **Table Editor**
2. يجب أن ترى هذه الجداول:
   ```
   ✅ users
   ✅ projects
   ✅ apartments
   ✅ sales
   ✅ contractors
   ✅ extracts
   ✅ suppliers
   ✅ invoices
   ✅ purchases
   ✅ maintenance_tasks
   ✅ daily_tasks
   ```

---

## الخطوة 3: الحصول على API Keys (2 دقيقة) 🔑

### 3.1 نسخ الإعدادات
1. اذهب إلى **Settings** > **API**
2. انسخ هذه القيم:

```javascript
Project URL: https://xxxxx.supabase.co
anon public key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 3.2 تحديث ملف supabase.js
1. افتح ملف `supabase.js`
2. استبدل هذه الأسطر:

```javascript
// قبل التحديث
const SUPABASE_URL = 'YOUR_SUPABASE_URL';
const SUPABASE_ANON_KEY = 'YOUR_SUPABASE_ANON_KEY';

// بعد التحديث
const SUPABASE_URL = 'https://xxxxx.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
```

---

## الخطوة 4: إعداد Authentication (3 دقائق) 🔐

### 4.1 إعداد Site URL
1. اذهب إلى **Authentication** > **Settings**
2. في **Site URL** أضف:
   ```
   https://your-app-name.netlify.app
   ```

### 4.2 إعداد Redirect URLs
1. في **Redirect URLs** أضف:
   ```
   https://your-app-name.netlify.app/auth/callback
   http://localhost:3000/auth/callback
   ```
2. اضغط **"Save"**

### 4.3 إنشاء مستخدم Admin
1. اذهب إلى **Authentication** > **Users**
2. اضغط **"Invite user"**
3. أدخل:
   ```
   Email: <EMAIL>
   Password: admin123
   ```
4. بعد إنشاء المستخدم، اضغط عليه
5. في **Raw User Meta Data** أضف:
   ```json
   {
     "role": "admin",
     "full_name": "مدير النظام"
   }
   ```
6. اضغط **"Save"**

---

## الخطوة 5: إضافة Supabase CDN (دقيقة واحدة) 📦

### 5.1 تحديث ملفات HTML
أضف هذا السطر في `<head>` لجميع ملفات HTML:

```html
<script src="https://unpkg.com/@supabase/supabase-js@2"></script>
```

### 5.2 مثال للإضافة في index.html:
```html
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المقاولات - تسجيل الدخول</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
```

---

## الخطوة 6: النشر والاختبار (5 دقائق) 🚀

### 6.1 النشر على Netlify
1. ارفع الملفات المحدثة إلى Netlify
2. انتظر انتهاء النشر

### 6.2 اختبار الاتصال
1. افتح الموقع
2. ابحث عن مؤشر الحالة في الشريط العلوي:
   - 🟢 **متصل** = نجح الربط مع Supabase
   - 🟡 **محلي** = يعمل في الوضع المحلي

### 6.3 اختبار تسجيل الدخول
1. استخدم الحساب الذي أنشأته:
   ```
   البريد: <EMAIL>
   كلمة المرور: admin123
   ```
2. يجب أن تدخل إلى النظام بنجاح

---

## 🔧 استكشاف الأخطاء

### مشكلة: مؤشر الحالة يظهر "محلي"
**الحل:**
1. تأكد من تحديث `SUPABASE_URL` و `SUPABASE_ANON_KEY`
2. تأكد من إضافة Supabase CDN
3. تحقق من وحدة التحكم للأخطاء

### مشكلة: خطأ في تسجيل الدخول
**الحل:**
1. تأكد من إنشاء المستخدم في Supabase
2. تحقق من Site URL في إعدادات Authentication
3. تأكد من صحة كلمة المرور

### مشكلة: خطأ في قاعدة البيانات
**الحل:**
1. تأكد من تشغيل `schema_fixed.sql` بنجاح
2. تحقق من وجود جميع الجداول في Table Editor
3. تأكد من تفعيل Row Level Security

---

## ✅ التحقق من نجاح الإعداد

بعد إكمال جميع الخطوات، يجب أن ترى:

1. **🟢 مؤشر "متصل"** في الشريط العلوي
2. **تسجيل دخول ناجح** بالحساب الجديد
3. **حفظ البيانات** في قاعدة البيانات الحقيقية
4. **مزامنة فورية** للتغييرات

---

## 🎉 تهانينا!

النظام الآن مربوط بقاعدة بيانات حقيقية ويمكن:
- ✅ **الوصول من أي مكان**
- ✅ **مشاركة البيانات** بين المستخدمين
- ✅ **النسخ الاحتياطي** التلقائي
- ✅ **الأمان المتقدم**
- ✅ **الأداء العالي**

**النظام جاهز للاستخدام الإنتاجي!** 🚀

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع **وحدة التحكم** للأخطاء
2. تحقق من **Logs** في Supabase
3. تأكد من **إعدادات Authentication**
4. راجع **Table Editor** للتأكد من البيانات
