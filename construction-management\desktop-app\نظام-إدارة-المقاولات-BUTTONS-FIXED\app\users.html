<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام إدارة المقاولات</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🏗️ نظام إدارة المقاولات</h2>
                <p>والتطوير العقاري</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="./dashboard.html" class="nav-item">
                    <span class="icon">📊</span>
                    الصفحة الرئيسية
                </a>
                <a href="./projects.html" class="nav-item">
                    <span class="icon">🏗️</span>
                    إدارة المشاريع
                </a>
                <a href="./sales.html" class="nav-item">
                    <span class="icon">🏢</span>
                    مبيعات الشقق
                </a>
                <a href="./contractors.html" class="nav-item">
                    <span class="icon">👷</span>
                    المقاولين والمستخلصات
                </a>
                <a href="./suppliers.html" class="nav-item">
                    <span class="icon">🚚</span>
                    الموردين والفواتير
                </a>
                <a href="./purchases.html" class="nav-item">
                    <span class="icon">🛒</span>
                    المشتريات
                </a>
                <a href="./maintenance.html" class="nav-item">
                    <span class="icon">🔧</span>
                    الصيانة والتشغيل
                </a>
                <a href="./tasks.html" class="nav-item">
                    <span class="icon">📋</span>
                    المهام اليومية
                </a>
                <a href="./reports.html" class="nav-item">
                    <span class="icon">📊</span>
                    التقارير المالية
                </a>
                <a href="./users.html" class="nav-item active">
                    <span class="icon">👥</span>
                    إدارة المستخدمين
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>إدارة المستخدمين</h1>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">أ</div>
                    <div>
                        <div id="userName">أحمد محمد</div>
                        <div style="font-size: 12px; color: #6b7280;" id="userRole">مدير النظام</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Page Header -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <div>
                        <h2 style="color: #374151; margin-bottom: 5px;">إدارة المستخدمين والصلاحيات</h2>
                        <p style="color: #6b7280;">إدارة حسابات المستخدمين ونظام الصلاحيات</p>
                    </div>
                    <button class="btn" onclick="openModal()">
                        ➕ مستخدم جديد
                    </button>
                </div>

                <!-- Users Stats -->
                <div class="stats-row">
                    <div class="mini-stat">
                        <div class="mini-stat-icon">👥</div>
                        <div class="mini-stat-number">15</div>
                        <div class="mini-stat-label">إجمالي المستخدمين</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">👨‍💼</div>
                        <div class="mini-stat-number">3</div>
                        <div class="mini-stat-label">المديرين</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">👨‍💻</div>
                        <div class="mini-stat-number">8</div>
                        <div class="mini-stat-label">الموظفين</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">🟢</div>
                        <div class="mini-stat-number">12</div>
                        <div class="mini-stat-label">نشط</div>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">قائمة المستخدمين (<span id="usersCount">6</span>)</h3>
                    </div>
                    <div class="card-content" style="padding: 0;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>آخر دخول</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="usersBody">
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 40px; height: 40px; background: #2563eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-left: 15px;">أ</div>
                                            <div>
                                                <div style="font-weight: bold;">أحمد محمد</div>
                                                <div style="font-size: 12px; color: #6b7280;">مدير النظام</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td><span class="status-badge status-hold">مدير النظام</span></td>
                                    <td>2024-01-15</td>
                                    <td>2024-12-26</td>
                                    <td><span class="status-badge status-completed">نشط</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; background: #f59e0b;">إيقاف</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 40px; height: 40px; background: #10b981; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-left: 15px;">ف</div>
                                            <div>
                                                <div style="font-weight: bold;">فاطمة أحمد</div>
                                                <div style="font-size: 12px; color: #6b7280;">مدير</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td><span class="status-badge status-progress">مدير</span></td>
                                    <td>2024-02-01</td>
                                    <td>2024-12-25</td>
                                    <td><span class="status-badge status-completed">نشط</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; background: #f59e0b;">إيقاف</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 40px; height: 40px; background: #8b5cf6; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-left: 15px;">م</div>
                                            <div>
                                                <div style="font-weight: bold;">محمد علي</div>
                                                <div style="font-size: 12px; color: #6b7280;">محاسب</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td><span class="status-badge status-planning">محاسب</span></td>
                                    <td>2024-03-10</td>
                                    <td>2024-12-26</td>
                                    <td><span class="status-badge status-completed">نشط</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; background: #f59e0b;">إيقاف</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 40px; height: 40px; background: #f59e0b; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-left: 15px;">س</div>
                                            <div>
                                                <div style="font-weight: bold;">سارة محمد</div>
                                                <div style="font-size: 12px; color: #6b7280;">موظف مبيعات</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td><span class="status-badge" style="background: #fef3c7; color: #92400e;">موظف</span></td>
                                    <td>2024-04-15</td>
                                    <td>2024-12-24</td>
                                    <td><span class="status-badge status-completed">نشط</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; background: #f59e0b;">إيقاف</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 40px; height: 40px; background: #ef4444; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-left: 15px;">خ</div>
                                            <div>
                                                <div style="font-weight: bold;">خالد أحمد</div>
                                                <div style="font-size: 12px; color: #6b7280;">مهندس مشاريع</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td><span class="status-badge" style="background: #fef3c7; color: #92400e;">موظف</span></td>
                                    <td>2024-05-20</td>
                                    <td>2024-12-23</td>
                                    <td><span class="status-badge" style="background: #fee2e2; color: #991b1b;">متوقف</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">تفعيل</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 40px; height: 40px; background: #06b6d4; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-left: 15px;">ن</div>
                                            <div>
                                                <div style="font-weight: bold;">نورا حسن</div>
                                                <div style="font-size: 12px; color: #6b7280;">موظف إداري</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><EMAIL></td>
                                    <td><span class="status-badge" style="background: #fef3c7; color: #92400e;">موظف</span></td>
                                    <td>2024-06-01</td>
                                    <td>2024-12-26</td>
                                    <td><span class="status-badge status-completed">نشط</span></td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;">تعديل</button>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px; background: #f59e0b;">إيقاف</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Permissions Overview -->
                <div class="card" style="margin-top: 30px;">
                    <div class="card-header">
                        <h3 class="card-title">نظرة عامة على الصلاحيات</h3>
                    </div>
                    <div class="card-content">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                            <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                                <h4 style="color: #ef4444; margin-bottom: 15px;">🔴 مدير النظام</h4>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="margin-bottom: 8px;">✅ جميع الصلاحيات</li>
                                    <li style="margin-bottom: 8px;">✅ إدارة المستخدمين</li>
                                    <li style="margin-bottom: 8px;">✅ إدارة النظام</li>
                                    <li style="margin-bottom: 8px;">✅ التقارير المالية</li>
                                </ul>
                            </div>
                            <div style="background: #f0f9ff; padding: 20px; border-radius: 8px;">
                                <h4 style="color: #3b82f6; margin-bottom: 15px;">🔵 مدير</h4>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="margin-bottom: 8px;">✅ إدارة المشاريع</li>
                                    <li style="margin-bottom: 8px;">✅ إدارة المبيعات</li>
                                    <li style="margin-bottom: 8px;">✅ إدارة المقاولين</li>
                                    <li style="margin-bottom: 8px;">❌ إدارة المستخدمين</li>
                                </ul>
                            </div>
                            <div style="background: #f0fdf4; padding: 20px; border-radius: 8px;">
                                <h4 style="color: #22c55e; margin-bottom: 15px;">🟢 محاسب</h4>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="margin-bottom: 8px;">✅ التقارير المالية</li>
                                    <li style="margin-bottom: 8px;">✅ إدارة الفواتير</li>
                                    <li style="margin-bottom: 8px;">❌ إدارة المشاريع</li>
                                    <li style="margin-bottom: 8px;">❌ إدارة المستخدمين</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="userModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>مستخدم جديد</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="userForm">
                <div class="form-group">
                    <label for="userName">الاسم الكامل *</label>
                    <input type="text" id="userName" required>
                </div>
                
                <div class="form-group">
                    <label for="userEmail">البريد الإلكتروني *</label>
                    <input type="email" id="userEmail" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="userPassword">كلمة المرور *</label>
                        <input type="password" id="userPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="userRole">الدور *</label>
                        <select id="userRole" required>
                            <option value="">اختر الدور</option>
                            <option value="admin">مدير النظام</option>
                            <option value="manager">مدير</option>
                            <option value="accountant">محاسب</option>
                            <option value="employee">موظف</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="userPhone">رقم الهاتف</label>
                        <input type="tel" id="userPhone">
                    </div>
                    <div class="form-group">
                        <label for="userDepartment">القسم</label>
                        <input type="text" id="userDepartment">
                    </div>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn" style="background: #6b7280;" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <script src="supabase.js"></script>
    <script src="auth.js"></script>
    <script>
        function openModal() {
            document.getElementById('userModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('userModal').style.display = 'none';
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('currentUser');
                window.location.href = './index.html';
            }
        }

        // تحقق من تسجيل الدخول والصلاحيات
        window.addEventListener('load', function() {
            const currentUser = localStorage.getItem('currentUser');
            if (!currentUser) {
                window.location.href = './index.html';
                return;
            }
            
            const user = JSON.parse(currentUser);
            
            // تحقق من صلاحية الوصول لهذه الصفحة
            if (user.role !== 'admin') {
                alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
                window.location.href = './dashboard.html';
                return;
            }
            
            document.getElementById('userName').textContent = user.name;
            document.getElementById('userAvatar').textContent = user.name.charAt(0);
            
            const roleNames = {
                admin: 'مدير النظام',
                manager: 'مدير',
                accountant: 'محاسب'
            };
            document.getElementById('userRole').textContent = roleNames[user.role] || user.role;
        });

        // إضافة أنماط الإحصائيات
        const style = document.createElement('style');
        style.textContent = `
            .stats-row {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 30px;
            }
            
            .mini-stat {
                background: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            
            .mini-stat-icon {
                font-size: 1.5rem;
                margin-bottom: 10px;
            }
            
            .mini-stat-number {
                font-size: 1.5rem;
                font-weight: bold;
                color: #2563eb;
                margin-bottom: 5px;
            }
            
            .mini-stat-label {
                color: #6b7280;
                font-size: 0.9rem;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
