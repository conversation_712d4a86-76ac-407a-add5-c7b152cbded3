[build]
  publish = "."
  command = "echo 'Static site - no build needed'"

[[redirects]]
  from = "/dashboard"
  to = "/dashboard.html"
  status = 200

[[redirects]]
  from = "/projects"
  to = "/projects.html"
  status = 200

[[redirects]]
  from = "/sales"
  to = "/sales.html"
  status = 200

[[redirects]]
  from = "/contractors"
  to = "/contractors.html"
  status = 200

[[redirects]]
  from = "/suppliers"
  to = "/suppliers.html"
  status = 200

[[redirects]]
  from = "/purchases"
  to = "/purchases.html"
  status = 200

[[redirects]]
  from = "/reports"
  to = "/reports.html"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NODE_VERSION = "18"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
