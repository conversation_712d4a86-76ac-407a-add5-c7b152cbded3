// Supabase Integration for Construction Management System
// ربط قاعدة البيانات مع Supabase

// إعدادات Supabase - محدثة بالبيانات الحقيقية
const SUPABASE_URL = 'https://xlpcpojmatiejxizukkz.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhscGNwb2ptYXRpZWp4aXp1a2t6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NDg0NzksImV4cCI6MjA2NjUyNDQ3OX0.eDNwBdyHfuQLeYqX_LVHdUekyHVmA3m9PH2yu_RV9Vc';

// تحقق من وجود إعدادات Supabase
const isSupabaseConfigured = true; // البيانات محدثة ومؤكدة

console.log('🔗 Supabase URL:', SUPABASE_URL);
console.log('🔑 API Key configured:', SUPABASE_ANON_KEY ? 'Yes' : 'No');
console.log('🔧 Supabase Configuration Status:', isSupabaseConfigured ? 'Configured' : 'Not Configured');

// إنشاء عميل Supabase
let supabase = null;
if (isSupabaseConfigured && typeof window !== 'undefined' && window.supabase) {
    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
}

// فئة إدارة قاعدة البيانات
class DatabaseManager {
    constructor() {
        this.isOnline = isSupabaseConfigured && supabase !== null;
        this.storagePrefix = 'construction_management_';
    }

    // تسجيل الدخول
    async signIn(email, password) {
        if (this.isOnline) {
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (error) throw error;
                
                // الحصول على بيانات المستخدم
                const { data: userData, error: userError } = await supabase
                    .from('users')
                    .select('*')
                    .eq('email', email)
                    .single();
                
                if (userError) throw userError;
                
                return { success: true, user: userData };
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                return { success: false, error: error.message };
            }
        } else {
            // وضع محلي - استخدام localStorage
            return this.localSignIn(email, password);
        }
    }

    // تسجيل الدخول المحلي
    localSignIn(email, password) {
        const validAccounts = {
            '<EMAIL>': { password: 'admin123', role: 'admin', name: 'أحمد محمد' },
            '<EMAIL>': { password: 'manager123', role: 'manager', name: 'فاطمة أحمد' },
            '<EMAIL>': { password: 'acc123', role: 'accountant', name: 'محمد علي' }
        };

        if (validAccounts[email] && validAccounts[email].password === password) {
            const user = {
                id: Date.now(),
                email: email,
                role: validAccounts[email].role,
                full_name: validAccounts[email].name
            };
            return { success: true, user: user };
        } else {
            return { success: false, error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة' };
        }
    }

    // تسجيل الخروج
    async signOut() {
        if (this.isOnline) {
            try {
                const { error } = await supabase.auth.signOut();
                if (error) throw error;
                return { success: true };
            } catch (error) {
                console.error('خطأ في تسجيل الخروج:', error);
                return { success: false, error: error.message };
            }
        } else {
            return { success: true };
        }
    }

    // الحصول على البيانات
    async getData(table, filters = {}) {
        if (this.isOnline) {
            try {
                let query = supabase.from(table).select('*');
                
                // تطبيق الفلاتر
                Object.keys(filters).forEach(key => {
                    query = query.eq(key, filters[key]);
                });
                
                const { data, error } = await query;
                if (error) throw error;
                
                return { success: true, data: data };
            } catch (error) {
                console.error(`خطأ في الحصول على بيانات ${table}:`, error);
                return { success: false, error: error.message };
            }
        } else {
            // وضع محلي - استخدام localStorage
            return this.getLocalData(table);
        }
    }

    // الحصول على البيانات المحلية
    getLocalData(table) {
        try {
            const data = localStorage.getItem(this.storagePrefix + table);
            return { 
                success: true, 
                data: data ? JSON.parse(data) : this.getDefaultData(table) 
            };
        } catch (error) {
            console.error(`خطأ في الحصول على البيانات المحلية ${table}:`, error);
            return { success: false, error: error.message };
        }
    }

    // إضافة بيانات
    async insertData(table, data) {
        if (this.isOnline) {
            try {
                const { data: result, error } = await supabase
                    .from(table)
                    .insert([data])
                    .select();
                
                if (error) throw error;
                
                return { success: true, data: result[0] };
            } catch (error) {
                console.error(`خطأ في إضافة بيانات ${table}:`, error);
                return { success: false, error: error.message };
            }
        } else {
            // وضع محلي
            return this.insertLocalData(table, data);
        }
    }

    // إضافة بيانات محلية
    insertLocalData(table, data) {
        try {
            const existingData = this.getLocalData(table).data || [];
            const newData = { ...data, id: Date.now() };
            existingData.push(newData);
            
            localStorage.setItem(this.storagePrefix + table, JSON.stringify(existingData));
            return { success: true, data: newData };
        } catch (error) {
            console.error(`خطأ في إضافة البيانات المحلية ${table}:`, error);
            return { success: false, error: error.message };
        }
    }

    // تحديث بيانات
    async updateData(table, id, data) {
        if (this.isOnline) {
            try {
                const { data: result, error } = await supabase
                    .from(table)
                    .update(data)
                    .eq('id', id)
                    .select();
                
                if (error) throw error;
                
                return { success: true, data: result[0] };
            } catch (error) {
                console.error(`خطأ في تحديث بيانات ${table}:`, error);
                return { success: false, error: error.message };
            }
        } else {
            // وضع محلي
            return this.updateLocalData(table, id, data);
        }
    }

    // تحديث بيانات محلية
    updateLocalData(table, id, data) {
        try {
            const existingData = this.getLocalData(table).data || [];
            const index = existingData.findIndex(item => item.id === id);
            
            if (index !== -1) {
                existingData[index] = { ...existingData[index], ...data };
                localStorage.setItem(this.storagePrefix + table, JSON.stringify(existingData));
                return { success: true, data: existingData[index] };
            } else {
                return { success: false, error: 'العنصر غير موجود' };
            }
        } catch (error) {
            console.error(`خطأ في تحديث البيانات المحلية ${table}:`, error);
            return { success: false, error: error.message };
        }
    }

    // حذف بيانات
    async deleteData(table, id) {
        if (this.isOnline) {
            try {
                const { error } = await supabase
                    .from(table)
                    .delete()
                    .eq('id', id);
                
                if (error) throw error;
                
                return { success: true };
            } catch (error) {
                console.error(`خطأ في حذف بيانات ${table}:`, error);
                return { success: false, error: error.message };
            }
        } else {
            // وضع محلي
            return this.deleteLocalData(table, id);
        }
    }

    // حذف بيانات محلية
    deleteLocalData(table, id) {
        try {
            const existingData = this.getLocalData(table).data || [];
            const filteredData = existingData.filter(item => item.id !== id);
            
            localStorage.setItem(this.storagePrefix + table, JSON.stringify(filteredData));
            return { success: true };
        } catch (error) {
            console.error(`خطأ في حذف البيانات المحلية ${table}:`, error);
            return { success: false, error: error.message };
        }
    }

    // الحصول على البيانات الافتراضية
    getDefaultData(table) {
        const defaultData = {
            projects: [
                {
                    id: 1,
                    name: 'مشروع الأندلس السكني',
                    description: 'مجمع سكني متكامل يضم 100 شقة',
                    location: 'القاهرة الجديدة',
                    budget: 50000000,
                    status: 'in_progress'
                },
                {
                    id: 2,
                    name: 'برج النيل التجاري',
                    description: 'برج تجاري بارتفاع 20 طابق',
                    location: 'وسط البلد',
                    budget: 75000000,
                    status: 'planning'
                }
            ],
            sales: [
                {
                    id: 1,
                    customer_name: 'أحمد محمد علي',
                    customer_phone: '01234567890',
                    apartment_number: '201',
                    project_name: 'مشروع الأندلس',
                    sale_price: 2500000,
                    down_payment: 500000,
                    status: 'completed'
                }
            ],
            contractors: [
                {
                    id: 1,
                    name: 'شركة البناء المتقدم',
                    phone: '01234567890',
                    specialty: 'أعمال الخرسانة والحديد',
                    address: 'القاهرة الجديدة'
                }
            ],
            suppliers: [
                {
                    id: 1,
                    name: 'شركة مواد البناء المتحدة',
                    phone: '01234567890',
                    category: 'مواد البناء',
                    address: 'القاهرة الجديدة'
                }
            ]
        };

        return defaultData[table] || [];
    }

    // تحقق من حالة الاتصال
    isConnected() {
        return this.isOnline;
    }

    // رسالة حالة الاتصال
    getConnectionStatus() {
        if (this.isOnline) {
            return { 
                status: 'online', 
                message: 'متصل بقاعدة البيانات السحابية' 
            };
        } else {
            return { 
                status: 'offline', 
                message: 'يعمل في الوضع المحلي - لربط قاعدة البيانات، يرجى تحديث إعدادات Supabase' 
            };
        }
    }
}

// إنشاء مثيل من مدير قاعدة البيانات
const db = new DatabaseManager();

// تصدير المدير للاستخدام في الملفات الأخرى
window.db = db;

// عرض حالة الاتصال في وحدة التحكم
console.log('🗄️ حالة قاعدة البيانات:', db.getConnectionStatus().message);

// إضافة مؤشر حالة الاتصال في الواجهة
document.addEventListener('DOMContentLoaded', function() {
    const connectionStatus = db.getConnectionStatus();
    
    // إضافة مؤشر في الشريط العلوي
    const header = document.querySelector('.header');
    if (header) {
        const statusIndicator = document.createElement('div');
        statusIndicator.style.cssText = `
            position: absolute;
            top: 5px;
            left: 20px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            ${connectionStatus.status === 'online' 
                ? 'background: #d1fae5; color: #065f46;' 
                : 'background: #fef3c7; color: #92400e;'
            }
        `;
        statusIndicator.textContent = connectionStatus.status === 'online' ? '🟢 متصل' : '🟡 محلي';
        statusIndicator.title = connectionStatus.message;
        
        header.style.position = 'relative';
        header.appendChild(statusIndicator);
    }
});

// دالة مساعدة لتحديث إعدادات Supabase
function updateSupabaseConfig(url, anonKey) {
    // يجب استدعاء هذه الدالة بعد الحصول على إعدادات Supabase الصحيحة
    console.log('لتحديث إعدادات Supabase، يرجى تعديل الملف supabase.js');
    console.log('SUPABASE_URL:', url);
    console.log('SUPABASE_ANON_KEY:', anonKey);
}
