<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المقاولين والمستخلصات - نظام إدارة المقاولات</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🏗️ نظام إدارة المقاولات</h2>
                <p>والتطوير العقاري</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.html" class="nav-item">
                    <span class="icon">📊</span>
                    الصفحة الرئيسية
                </a>
                <a href="projects.html" class="nav-item">
                    <span class="icon">🏗️</span>
                    إدارة المشاريع
                </a>
                <a href="sales.html" class="nav-item">
                    <span class="icon">🏢</span>
                    مبيعات الشقق
                </a>
                <a href="contractors.html" class="nav-item active">
                    <span class="icon">👷</span>
                    المقاولين والمستخلصات
                </a>
                <a href="suppliers.html" class="nav-item">
                    <span class="icon">🚚</span>
                    الموردين والفواتير
                </a>
                <a href="purchases.html" class="nav-item">
                    <span class="icon">🛒</span>
                    المشتريات
                </a>
                <a href="maintenance.html" class="nav-item">
                    <span class="icon">🔧</span>
                    الصيانة والتشغيل
                </a>
                <a href="tasks.html" class="nav-item">
                    <span class="icon">📋</span>
                    المهام اليومية
                </a>
                <a href="reports.html" class="nav-item">
                    <span class="icon">📊</span>
                    التقارير المالية
                </a>
                <a href="users.html" class="nav-item">
                    <span class="icon">👥</span>
                    إدارة المستخدمين
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>المقاولين والمستخلصات</h1>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">أ</div>
                    <div>
                        <div id="userName">أحمد محمد</div>
                        <div style="font-size: 12px; color: #6b7280;" id="userRole">مدير النظام</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Tabs -->
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; border-bottom: 2px solid #e5e7eb;">
                        <button class="tab-btn active" onclick="switchTab('contractors')">المقاولين</button>
                        <button class="tab-btn" onclick="switchTab('extracts')">المستخلصات</button>
                    </div>
                </div>

                <!-- Contractors Tab -->
                <div id="contractorsTab" class="tab-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <div>
                            <h2 style="color: #374151; margin-bottom: 5px;">إدارة المقاولين</h2>
                            <p style="color: #6b7280;">إدارة بيانات المقاولين والتخصصات</p>
                        </div>
                        <button class="btn" onclick="openContractorModal()">
                            ➕ مقاول جديد
                        </button>
                    </div>

                    <!-- Contractors Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">قائمة المقاولين (<span id="contractorsCount">3</span>)</h3>
                        </div>
                        <div class="card-content" style="padding: 0;">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>اسم المقاول</th>
                                        <th>التخصص</th>
                                        <th>رقم الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>العنوان</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="contractorsBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Extracts Tab -->
                <div id="extractsTab" class="tab-content" style="display: none;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <div>
                            <h2 style="color: #374151; margin-bottom: 5px;">إدارة المستخلصات</h2>
                            <p style="color: #6b7280;">إدارة المستخلصات المالية للمقاولين</p>
                        </div>
                        <button class="btn" onclick="openExtractModal()">
                            ➕ مستخلص جديد
                        </button>
                    </div>

                    <!-- Extracts Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">قائمة المستخلصات (<span id="extractsCount">4</span>)</h3>
                        </div>
                        <div class="card-content" style="padding: 0;">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>المقاول</th>
                                        <th>المشروع</th>
                                        <th>المبلغ</th>
                                        <th>الوصف</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="extractsBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contractor Modal -->
    <div id="contractorModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="contractorModalTitle">مقاول جديد</h3>
                <span class="close" onclick="closeContractorModal()">&times;</span>
            </div>
            
            <form id="contractorForm">
                <div class="form-group">
                    <label for="contractorName">اسم المقاول *</label>
                    <input type="text" id="contractorName" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="contractorPhone">رقم الهاتف</label>
                        <input type="tel" id="contractorPhone">
                    </div>
                    <div class="form-group">
                        <label for="contractorEmail">البريد الإلكتروني</label>
                        <input type="email" id="contractorEmail">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="contractorSpecialty">التخصص</label>
                    <input type="text" id="contractorSpecialty" placeholder="مثال: أعمال الخرسانة">
                </div>
                
                <div class="form-group">
                    <label for="contractorAddress">العنوان</label>
                    <textarea id="contractorAddress" rows="3"></textarea>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn" style="background: #6b7280;" onclick="closeContractorModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Extract Modal -->
    <div id="extractModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="extractModalTitle">مستخلص جديد</h3>
                <span class="close" onclick="closeExtractModal()">&times;</span>
            </div>
            
            <form id="extractForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="extractContractor">المقاول *</label>
                        <select id="extractContractor" required>
                            <option value="">اختر المقاول</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="extractProject">المشروع *</label>
                        <select id="extractProject" required>
                            <option value="">اختر المشروع</option>
                            <option value="1">مشروع الأندلس السكني</option>
                            <option value="2">برج النيل التجاري</option>
                            <option value="3">مجمع الزهراء السكني</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="extractAmount">المبلغ *</label>
                        <input type="number" id="extractAmount" required placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label for="extractDate">تاريخ المستخلص</label>
                        <input type="date" id="extractDate">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="extractDescription">الوصف</label>
                    <textarea id="extractDescription" rows="3" placeholder="وصف الأعمال المنجزة"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="extractStatus">الحالة</label>
                    <select id="extractStatus">
                        <option value="pending">معلق</option>
                        <option value="approved">معتمد</option>
                        <option value="paid">مدفوع</option>
                    </select>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn" style="background: #6b7280;" onclick="closeExtractModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <script src="supabase.js"></script>
    <script src="auth.js"></script>
    <script src="fix-buttons.js"></script>
    <script>
        // بيانات المقاولين
        let contractors = [
            {
                id: 1,
                name: 'شركة البناء المتقدم',
                phone: '01234567890',
                email: '<EMAIL>',
                specialty: 'أعمال الخرسانة والحديد',
                address: 'القاهرة الجديدة، التجمع الأول'
            },
            {
                id: 2,
                name: 'مقاولات الإخوة للتشطيبات',
                phone: '01098765432',
                email: '<EMAIL>',
                specialty: 'أعمال التشطيبات والديكور',
                address: 'مدينة نصر، القاهرة'
            },
            {
                id: 3,
                name: 'شركة النيل للكهرباء',
                phone: '01555666777',
                email: '<EMAIL>',
                specialty: 'الأعمال الكهربائية',
                address: 'المعادي، القاهرة'
            }
        ];

        // بيانات المستخلصات
        let extracts = [
            {
                id: 1,
                contractorId: 1,
                contractorName: 'شركة البناء المتقدم',
                projectName: 'مشروع الأندلس السكني',
                amount: 500000,
                description: 'أعمال الخرسانة للطابق الأول',
                extractDate: '2024-12-20',
                status: 'approved'
            },
            {
                id: 2,
                contractorId: 2,
                contractorName: 'مقاولات الإخوة للتشطيبات',
                projectName: 'برج النيل التجاري',
                amount: 300000,
                description: 'تشطيبات الطوابق 1-3',
                extractDate: '2024-12-19',
                status: 'paid'
            },
            {
                id: 3,
                contractorId: 3,
                contractorName: 'شركة النيل للكهرباء',
                projectName: 'مجمع الزهراء السكني',
                amount: 150000,
                description: 'تمديدات كهربائية',
                extractDate: '2024-12-18',
                status: 'pending'
            },
            {
                id: 4,
                contractorId: 1,
                contractorName: 'شركة البناء المتقدم',
                projectName: 'مشروع الأندلس السكني',
                amount: 750000,
                description: 'أعمال الخرسانة للطابق الثاني',
                extractDate: '2024-12-17',
                status: 'approved'
            }
        ];

        let editingContractorId = null;
        let editingExtractId = null;

        // تبديل التبويبات
        function switchTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            
            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tabName + 'Tab').style.display = 'block';
            
            // إضافة الفئة النشطة للزر المحدد
            event.target.classList.add('active');
        }

        // عرض المقاولين
        function displayContractors() {
            const tbody = document.getElementById('contractorsBody');
            tbody.innerHTML = '';

            contractors.forEach(contractor => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: bold;">${contractor.name}</div>
                    </td>
                    <td>${contractor.specialty || '-'}</td>
                    <td>${contractor.phone || '-'}</td>
                    <td>${contractor.email || '-'}</td>
                    <td>${contractor.address || '-'}</td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;" onclick="editContractor(${contractor.id})">تعديل</button>
                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteContractor(${contractor.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('contractorsCount').textContent = contractors.length;
            updateContractorSelect();
        }

        // عرض المستخلصات
        function displayExtracts() {
            const tbody = document.getElementById('extractsBody');
            tbody.innerHTML = '';

            extracts.forEach(extract => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${extract.contractorName}</td>
                    <td>${extract.projectName}</td>
                    <td>${formatCurrency(extract.amount)}</td>
                    <td>${extract.description || '-'}</td>
                    <td>${formatDate(extract.extractDate)}</td>
                    <td>${getStatusBadge(extract.status)}</td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;" onclick="editExtract(${extract.id})">تعديل</button>
                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteExtract(${extract.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('extractsCount').textContent = extracts.length;
        }

        // تحديث قائمة المقاولين في النموذج
        function updateContractorSelect() {
            const select = document.getElementById('extractContractor');
            select.innerHTML = '<option value="">اختر المقاول</option>';
            
            contractors.forEach(contractor => {
                const option = document.createElement('option');
                option.value = contractor.id;
                option.textContent = contractor.name;
                select.appendChild(option);
            });
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-EG').format(amount) + ' ج.م';
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-EG');
        }

        // شارة الحالة
        function getStatusBadge(status) {
            const statusMap = {
                pending: { label: 'معلق', class: 'status-planning' },
                approved: { label: 'معتمد', class: 'status-progress' },
                paid: { label: 'مدفوع', class: 'status-completed' }
            };
            
            const statusInfo = statusMap[status] || { label: status, class: 'status-planning' };
            return `<span class="status-badge ${statusInfo.class}">${statusInfo.label}</span>`;
        }

        // نوافذ المقاولين
        function openContractorModal() {
            document.getElementById('contractorModal').style.display = 'block';
            document.getElementById('contractorModalTitle').textContent = 'مقاول جديد';
            document.getElementById('contractorForm').reset();
            editingContractorId = null;
        }

        function closeContractorModal() {
            document.getElementById('contractorModal').style.display = 'none';
        }

        function editContractor(id) {
            const contractor = contractors.find(c => c.id === id);
            if (contractor) {
                editingContractorId = id;
                document.getElementById('contractorModalTitle').textContent = 'تعديل المقاول';
                document.getElementById('contractorName').value = contractor.name;
                document.getElementById('contractorPhone').value = contractor.phone || '';
                document.getElementById('contractorEmail').value = contractor.email || '';
                document.getElementById('contractorSpecialty').value = contractor.specialty || '';
                document.getElementById('contractorAddress').value = contractor.address || '';
                document.getElementById('contractorModal').style.display = 'block';
            }
        }

        function deleteContractor(id) {
            if (confirm('هل أنت متأكد من حذف هذا المقاول؟')) {
                contractors = contractors.filter(c => c.id !== id);
                displayContractors();
            }
        }

        // نوافذ المستخلصات
        function openExtractModal() {
            document.getElementById('extractModal').style.display = 'block';
            document.getElementById('extractModalTitle').textContent = 'مستخلص جديد';
            document.getElementById('extractForm').reset();
            editingExtractId = null;
        }

        function closeExtractModal() {
            document.getElementById('extractModal').style.display = 'none';
        }

        function editExtract(id) {
            const extract = extracts.find(e => e.id === id);
            if (extract) {
                editingExtractId = id;
                document.getElementById('extractModalTitle').textContent = 'تعديل المستخلص';
                document.getElementById('extractContractor').value = extract.contractorId;
                document.getElementById('extractAmount').value = extract.amount;
                document.getElementById('extractDescription').value = extract.description || '';
                document.getElementById('extractDate').value = extract.extractDate || '';
                document.getElementById('extractStatus').value = extract.status;
                document.getElementById('extractModal').style.display = 'block';
            }
        }

        function deleteExtract(id) {
            if (confirm('هل أنت متأكد من حذف هذا المستخلص؟')) {
                extracts = extracts.filter(e => e.id !== id);
                displayExtracts();
            }
        }

        // حفظ المقاول
        document.getElementById('contractorForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const contractorData = {
                name: document.getElementById('contractorName').value,
                phone: document.getElementById('contractorPhone').value,
                email: document.getElementById('contractorEmail').value,
                specialty: document.getElementById('contractorSpecialty').value,
                address: document.getElementById('contractorAddress').value
            };

            if (editingContractorId) {
                const index = contractors.findIndex(c => c.id === editingContractorId);
                if (index !== -1) {
                    contractors[index] = { ...contractors[index], ...contractorData };
                }
            } else {
                const newContractor = {
                    id: Date.now(),
                    ...contractorData
                };
                contractors.push(newContractor);
            }

            displayContractors();
            closeContractorModal();
        });

        // حفظ المستخلص
        document.getElementById('extractForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const contractorId = parseInt(document.getElementById('extractContractor').value);
            const contractor = contractors.find(c => c.id === contractorId);

            const extractData = {
                contractorId: contractorId,
                contractorName: contractor ? contractor.name : '',
                projectName: 'مشروع الأندلس السكني', // يمكن ربطها بالمشروع المختار
                amount: parseFloat(document.getElementById('extractAmount').value),
                description: document.getElementById('extractDescription').value,
                extractDate: document.getElementById('extractDate').value,
                status: document.getElementById('extractStatus').value
            };

            if (editingExtractId) {
                const index = extracts.findIndex(e => e.id === editingExtractId);
                if (index !== -1) {
                    extracts[index] = { ...extracts[index], ...extractData };
                }
            } else {
                const newExtract = {
                    id: Date.now(),
                    ...extractData
                };
                extracts.push(newExtract);
            }

            displayExtracts();
            closeExtractModal();
        });

        // تهيئة الصفحة
        window.addEventListener('load', function() {
            checkAuthAndUpdateUI();
            displayContractors();
            displayExtracts();
        });

        // إضافة أنماط التبويبات
        const style = document.createElement('style');
        style.textContent = `
            .tab-btn {
                padding: 12px 24px;
                background: none;
                border: none;
                border-bottom: 2px solid transparent;
                cursor: pointer;
                font-size: 16px;
                color: #6b7280;
                transition: all 0.3s;
            }
            
            .tab-btn.active {
                color: #2563eb;
                border-bottom-color: #2563eb;
            }
            
            .tab-btn:hover {
                color: #2563eb;
            }
        `;
        document.head.appendChild(style);
    </script>

    <!-- إضافة ملفات الإصلاح المطلوبة -->
    <script src="supabase.js"></script>
    <script src="auth.js"></script>
    <script src="fix-buttons.js"></script>
    <script src="button-fix-universal.js"></script>

    <script>
        // التأكد من تحميل قاعدة البيانات
        if (typeof db === 'undefined') {
            console.error('قاعدة البيانات غير محملة - تحقق من ملف supabase.js');
        } else {
            console.log('تم تحميل قاعدة البيانات بنجاح');
        }
    </script>
</body>
</html>
