# 🚀 دليل البدء السريع - نظام إدارة المقاولات

## 📋 النشر السريع (دقيقتان)

### الخطوة 1: النشر على Netlify
1. **اذهب إلى** [netlify.com](https://netlify.com)
2. **احذف أي نشر سابق** (إن وجد)
3. **اسحب وأفلت مجلد `static-version` بالكامل**
4. **انتظر انتهاء النشر** (30-60 ثانية)

### الخطوة 2: اختبار النظام
1. **افتح الرابط** الذي يظهر بعد النشر
2. **جرب تسجيل الدخول** بأحد الحسابات التجريبية
3. **استكشف جميع الوحدات** من الشريط الجانبي

---

## 🔑 الحسابات التجريبية

### 👨‍💼 مدير النظام (جميع الصلاحيات)
```
البريد: <EMAIL>
كلمة المرور: admin123
```

### 👨‍💼 مدير (صلاحيات محدودة)
```
البريد: <EMAIL>
كلمة المرور: manager123
```

### 👨‍💼 محاسب (صلاحيات مالية)
```
البريد: <EMAIL>
كلمة المرور: acc123
```

---

## 📱 الوحدات المتاحة

### 🏠 **الصفحة الرئيسية**
- إحصائيات شاملة للنظام
- نظرة عامة على المشاريع والمبيعات
- إجراءات سريعة

### 🏗️ **إدارة المشاريع**
- إضافة وتعديل وحذف المشاريع
- تتبع حالة المشاريع
- بحث وتصفية متقدم

### 🏢 **مبيعات الشقق**
- إدارة العملاء والمبيعات
- تتبع المدفوعات والأقساط
- إحصائيات المبيعات

### 👷 **المقاولين والمستخلصات**
- إدارة بيانات المقاولين
- تتبع المستخلصات المالية
- حساب المدفوعات

### 🚚 **الموردين والفواتير**
- إدارة بيانات الموردين
- تتبع الفواتير والمدفوعات
- إحصائيات الموردين

### 🛒 **المشتريات**
- إدارة طلبات الشراء
- تتبع حالة الطلبات
- حساب التكاليف

### 🔧 **الصيانة والتشغيل**
- إدارة مهام الصيانة
- تحديد الأولويات
- تتبع التنفيذ

### 📋 **المهام اليومية**
- تنظيم المهام اليومية
- تحديد المسؤوليات
- تتبع التقدم

### 👥 **إدارة المستخدمين** (مدير النظام فقط)
- إضافة وإدارة المستخدمين
- تحديد الصلاحيات
- نظام الأدوار

### 📊 **التقارير المالية**
- تقارير شاملة ومتنوعة
- تصدير PDF
- طباعة مباشرة

---

## 🎯 اختبار سريع للنظام

### 1. تسجيل الدخول
- استخدم حساب `<EMAIL>` / `admin123`
- يجب أن تنتقل إلى لوحة التحكم

### 2. إضافة مشروع جديد
- اذهب إلى "إدارة المشاريع"
- اضغط "مشروع جديد"
- املأ البيانات واحفظ

### 3. إضافة مبيعة
- اذهب إلى "مبيعات الشقق"
- اضغط "مبيعة جديدة"
- املأ بيانات العميل والشقة

### 4. إنشاء تقرير
- اذهب إلى "التقارير المالية"
- اختر نوع التقرير
- اضغط "تصدير PDF"

### 5. إدارة المستخدمين
- اذهب إلى "إدارة المستخدمين"
- اضغط "مستخدم جديد"
- أضف مستخدم بدور محدد

---

## 🔧 الميزات المتقدمة

### 📊 **الإحصائيات التفاعلية**
- إحصائيات في الوقت الفعلي
- رسوم بيانية تفاعلية
- مؤشرات الأداء

### 🔍 **البحث والتصفية**
- بحث متقدم في جميع الوحدات
- تصفية حسب معايير متعددة
- ترتيب النتائج

### 📱 **التصميم المتجاوب**
- يعمل على جميع الأجهزة
- واجهة متكيفة
- تجربة مستخدم محسنة

### 🔐 **نظام الصلاحيات**
- أدوار متعددة المستويات
- تحكم دقيق في الوصول
- أمان متقدم

### 📄 **تقارير PDF**
- تصدير احترافي
- تصميم متطور
- طباعة مباشرة

---

## 🛠️ التخصيص والتطوير

### تعديل البيانات
- البيانات التجريبية في ملفات JavaScript
- يمكن تعديلها بسهولة
- إضافة بيانات جديدة

### تخصيص التصميم
- جميع الأنماط في `styles.css`
- متغيرات CSS للألوان
- سهولة التخصيص

### إضافة وحدات جديدة
- نمط موحد للوحدات
- سهولة الإضافة
- توثيق واضح

---

## 📞 الدعم والمساعدة

### مشاكل شائعة:

#### **لا تظهر الصفحات الأخرى**
- تأكد من رفع جميع الملفات
- امسح cache المتصفح
- تحقق من ملف `_redirects`

#### **لا يعمل تسجيل الدخول**
- تأكد من استخدام الحسابات الصحيحة
- تحقق من وحدة التحكم للأخطاء
- امسح localStorage

#### **مشاكل في التصميم**
- تأكد من رفع ملف `styles.css`
- امسح cache المتصفح
- تحقق من مسارات الملفات

### للحصول على المساعدة:
1. تحقق من وحدة التحكم (F12)
2. راجع ملفات التوثيق
3. تأكد من رفع جميع الملفات

---

## 🎉 تهانينا!

**لديك الآن نظام إدارة مقاولات متكامل يحتوي على:**

✅ **11 وحدة كاملة**
✅ **نظام صلاحيات متقدم**
✅ **تقارير PDF احترافية**
✅ **واجهة عربية متطورة**
✅ **تصميم متجاوب**
✅ **بيانات تجريبية شاملة**

**النظام جاهز للاستخدام الإنتاجي!** 🚀

---

## 📋 قائمة المراجعة

- [ ] تم نشر النظام على Netlify
- [ ] تم اختبار تسجيل الدخول
- [ ] تم اختبار جميع الوحدات
- [ ] تم اختبار التقارير
- [ ] تم اختبار إدارة المستخدمين
- [ ] تم اختبار التصميم المتجاوب

**عند إكمال جميع النقاط، النظام جاهز للاستخدام!** ✅
