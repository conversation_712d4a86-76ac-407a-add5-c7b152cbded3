# إنشاء تطبيق محمول لنظام إدارة المقاولات

Write-Host "========================================" -ForegroundColor Green
Write-Host "   إنشاء تطبيق محمول لنظام إدارة المقاولات" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# التحقق من Node.js
Write-Host "🔧 التحقق من Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js مثبت بنجاح - الإصدار: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير مثبت!" -ForegroundColor Red
    Write-Host "📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""

# التحقق من المكتبات
Write-Host "📦 التحقق من المكتبات..." -ForegroundColor Yellow
if (-not (Test-Path "node_modules")) {
    Write-Host "🔄 تثبيت المكتبات المطلوبة..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ فشل في تثبيت المكتبات!" -ForegroundColor Red
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
}

Write-Host "✅ المكتبات جاهزة" -ForegroundColor Green
Write-Host ""

# إنشاء التطبيق المحمول
Write-Host "🏗️ إنشاء التطبيق المحمول..." -ForegroundColor Yellow

# حذف المجلد القديم إن وجد
if (Test-Path "portable-app") {
    Remove-Item "portable-app" -Recurse -Force
}

# إنشاء مجلد جديد
New-Item -ItemType Directory -Name "portable-app" -Force | Out-Null

Write-Host "📁 نسخ ملفات التطبيق..." -ForegroundColor Yellow

# نسخ الملفات الأساسية
Copy-Item "main.js" "portable-app\" -Force
Copy-Item "preload.js" "portable-app\" -Force
Copy-Item "package.json" "portable-app\" -Force

# نسخ مجلد التطبيق
if (Test-Path "app") {
    Copy-Item "app" "portable-app\app" -Recurse -Force
}

# إنشاء ملف تشغيل
$startBatContent = @"
@echo off
title نظام إدارة المقاولات
echo بدء تشغيل نظام إدارة المقاولات...
npx electron .
pause
"@

$startBatContent | Out-File "portable-app\start.bat" -Encoding ASCII

# إنشاء ملف README
$readmeContent = @"
# نظام إدارة المقاولات - تطبيق محمول

## طريقة التشغيل:
1. تأكد من تثبيت Node.js على الجهاز
2. شغل start.bat
3. انتظر فتح التطبيق

## متطلبات التشغيل:
- Node.js (حمل من: https://nodejs.org/)
- Windows 7/8/10/11
- 2 GB RAM على الأقل

## بيانات تسجيل الدخول:
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: admin123

## الميزات:
- إدارة المشاريع
- مبيعات الشقق
- إدارة المقاولين والمستخلصات
- إدارة الموردين والفواتير
- إدارة المشتريات
- التقارير المالية
- إدارة المستخدمين

## الدعم:
إذا واجهت أي مشاكل، تأكد من:
1. تثبيت Node.js
2. اتصال الإنترنت (لقاعدة البيانات)
3. تشغيل start.bat كمدير إذا لزم الأمر
"@

$readmeContent | Out-File "portable-app\README.md" -Encoding UTF8

Write-Host "✅ تم إنشاء التطبيق المحمول بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "📁 ستجد التطبيق في مجلد: portable-app\" -ForegroundColor Cyan
Write-Host "🚀 لتشغيل التطبيق: شغل start.bat من داخل مجلد portable-app" -ForegroundColor Cyan
Write-Host ""
Write-Host "📦 يمكنك نسخ مجلد portable-app إلى أي جهاز آخر" -ForegroundColor Yellow
Write-Host ""

# فتح مجلد التطبيق المحمول
if (Test-Path "portable-app") {
    Write-Host "📂 فتح مجلد التطبيق المحمول..." -ForegroundColor Yellow
    Start-Process explorer "portable-app"
}

Write-Host "🎉 اكتمل إنشاء التطبيق المحمول!" -ForegroundColor Green
Read-Host "اضغط Enter للخروج"
