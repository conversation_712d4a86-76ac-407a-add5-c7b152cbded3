const { app, BrowserWindow, Menu, dialog, shell } = require('electron');
const path = require('path');

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';

let mainWindow;

function createWindow() {
    // إنشاء النافذة الرئيسية
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 700,
        icon: path.join(__dirname, 'assets', 'icon.png'),
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js'),
            webSecurity: false // للسماح بتحميل الملفات المحلية
        },
        titleBarStyle: 'default',
        show: false // لا تظهر النافذة حتى تكون جاهزة
    });

    // تحميل التطبيق
    mainWindow.loadFile(path.join(__dirname, 'app', 'index.html'));

    // إظهار النافذة عندما تكون جاهزة
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // تركيز النافذة
        if (process.platform === 'darwin') {
            app.dock.show();
        }
    });

    // إعداد القائمة
    createMenu();

    // التعامل مع إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // فتح الروابط الخارجية في المتصفح
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // إعداد Developer Tools في بيئة التطوير
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }
}

function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'صفحة جديدة',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.reload();
                    }
                },
                {
                    label: 'إعادة تحميل',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        mainWindow.reload();
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                { type: 'separator' },
                { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
                { type: 'separator' },
                { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
                { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
                { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
                { type: 'separator' },
                { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
            ]
        },
        {
            label: 'نافذة',
            submenu: [
                { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
                { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول التطبيق',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول التطبيق',
                            message: 'نظام إدارة المقاولات والتطوير العقاري',
                            detail: 'الإصدار 1.0.0\nتطبيق شامل لإدارة المشاريع والمبيعات والمقاولين',
                            buttons: ['موافق']
                        });
                    }
                },
                {
                    label: 'دليل الاستخدام',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'دليل الاستخدام',
                            message: 'كيفية استخدام التطبيق',
                            detail: '1. سجل دخول بالبيانات المطلوبة\n2. اختر القسم المطلوب من القائمة الجانبية\n3. استخدم أزرار الإضافة والتعديل\n4. احفظ البيانات',
                            buttons: ['موافق']
                        });
                    }
                }
            ]
        }
    ];

    // تعديل القائمة لنظام macOS
    if (process.platform === 'darwin') {
        template.unshift({
            label: app.getName(),
            submenu: [
                { label: 'حول ' + app.getName(), role: 'about' },
                { type: 'separator' },
                { label: 'خدمات', role: 'services', submenu: [] },
                { type: 'separator' },
                { label: 'إخفاء ' + app.getName(), accelerator: 'Command+H', role: 'hide' },
                { label: 'إخفاء الآخرين', accelerator: 'Command+Shift+H', role: 'hideothers' },
                { label: 'إظهار الكل', role: 'unhide' },
                { type: 'separator' },
                { label: 'خروج', accelerator: 'Command+Q', click: () => app.quit() }
            ]
        });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// التعامل مع أحداث التطبيق
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// منع فتح نوافذ متعددة
app.on('second-instance', () => {
    if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore();
        mainWindow.focus();
    }
});

// جعل التطبيق instance واحد فقط
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
    app.quit();
}
