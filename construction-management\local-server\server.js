const express = require('express');
const path = require('path');
const cors = require('cors');
const { exec } = require('child_process');

const app = express();
const PORT = 3000;

// إعداد CORS للسماح بالوصول من أجهزة أخرى
app.use(cors());

// تقديم الملفات الثابتة
app.use(express.static(path.join(__dirname, 'public')));

// إعداد JSON parsing
app.use(express.json());

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API للحصول على معلومات الخادم
app.get('/api/server-info', (req, res) => {
    res.json({
        status: 'running',
        port: PORT,
        timestamp: new Date().toISOString(),
        message: 'خادم نظام إدارة المقاولات يعمل بنجاح'
    });
});

// API لإعادة تشغيل الخادم
app.post('/api/restart', (req, res) => {
    res.json({ message: 'سيتم إعادة تشغيل الخادم...' });
    setTimeout(() => {
        process.exit(0);
    }, 1000);
});

// معالجة الأخطاء
app.use((err, req, res, next) => {
    console.error('خطأ في الخادم:', err);
    res.status(500).json({ error: 'خطأ داخلي في الخادم' });
});

// بدء الخادم
app.listen(PORT, '0.0.0.0', () => {
    console.log('🚀 خادم نظام إدارة المقاولات يعمل الآن!');
    console.log(`📍 العنوان المحلي: http://localhost:${PORT}`);
    console.log(`🌐 عنوان الشبكة: http://[IP-ADDRESS]:${PORT}`);
    console.log('📋 للوصول من أجهزة أخرى، استخدم عنوان IP الخاص بهذا الجهاز');
    console.log('⏹️  لإيقاف الخادم: اضغط Ctrl+C');
    
    // محاولة فتح المتصفح تلقائياً
    const url = `http://localhost:${PORT}`;
    console.log(`🔗 فتح المتصفح: ${url}`);
    
    // فتح المتصفح حسب نظام التشغيل
    const platform = process.platform;
    let command;
    
    if (platform === 'win32') {
        command = `start ${url}`;
    } else if (platform === 'darwin') {
        command = `open ${url}`;
    } else {
        command = `xdg-open ${url}`;
    }
    
    exec(command, (error) => {
        if (error) {
            console.log('💡 افتح المتصفح يدوياً واذهب إلى:', url);
        }
    });
});

// معالجة إشارات الإغلاق
process.on('SIGINT', () => {
    console.log('\n⏹️  تم إيقاف الخادم بواسطة المستخدم');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n⏹️  تم إيقاف الخادم');
    process.exit(0);
});

// معالجة الأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
    console.error('❌ خطأ غير متوقع:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ رفض غير معالج:', reason);
    process.exit(1);
});
