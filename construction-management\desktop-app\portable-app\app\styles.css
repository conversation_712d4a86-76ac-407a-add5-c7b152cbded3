/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Login Page Styles */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.login-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 450px;
}

.logo {
    text-align: center;
    margin-bottom: 30px;
}

.logo-icon {
    font-size: 4rem;
    margin-bottom: 10px;
}

.logo h1 {
    color: #2563eb;
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.logo p {
    color: #6b7280;
    font-size: 1rem;
}

.login-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #374151;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #2563eb;
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox input {
    margin-left: 8px;
    width: auto;
}

.forgot-password {
    color: #2563eb;
    text-decoration: none;
    font-size: 14px;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    background: #2563eb;
    color: white;
    border: none;
    padding: 15px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s;
}

.login-btn:hover {
    background: #1d4ed8;
}

.demo-accounts {
    background: #f8fafc;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.demo-accounts h3 {
    color: #374151;
    margin-bottom: 15px;
    font-size: 1rem;
}

.demo-account {
    background: white;
    padding: 10px 15px;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: background 0.3s;
    border: 1px solid #e5e7eb;
}

.demo-account:hover {
    background: #e5e7eb;
}

.demo-account:last-child {
    margin-bottom: 0;
}

.register-link {
    text-align: center;
    color: #6b7280;
}

.register-link a {
    color: #2563eb;
    text-decoration: none;
}

.register-link a:hover {
    text-decoration: underline;
}

/* Dashboard Styles */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    background: #f8fafc;
}

.sidebar {
    width: 280px;
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.sidebar-header {
    padding: 20px;
    background: #2563eb;
    color: white;
    text-align: center;
}

.sidebar-header h2 {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    display: block;
    padding: 15px 25px;
    color: #374151;
    text-decoration: none;
    transition: background 0.3s;
    border-right: 3px solid transparent;
}

.nav-item:hover, .nav-item.active {
    background: #f3f4f6;
    border-right-color: #2563eb;
    color: #2563eb;
}

.nav-item .icon {
    margin-left: 10px;
    font-size: 1.2rem;
}

.main-content {
    margin-right: 280px;
    flex: 1;
    padding: 0;
}

.header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    color: #374151;
    font-size: 1.8rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: #2563eb;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.logout-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
}

.logout-btn:hover {
    background: #dc2626;
}

.content {
    padding: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #2563eb;
    margin-bottom: 5px;
}

.stat-label {
    color: #6b7280;
    font-size: 1rem;
}

.card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e5e7eb;
    background: #f8fafc;
}

.card-title {
    font-size: 1.3rem;
    color: #374151;
    margin: 0;
}

.card-content {
    padding: 25px;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #2563eb;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.btn:hover {
    background: #1d4ed8;
}

.btn-success {
    background: #10b981;
}

.btn-success:hover {
    background: #059669;
}

.btn-danger {
    background: #ef4444;
}

.btn-danger:hover {
    background: #dc2626;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
