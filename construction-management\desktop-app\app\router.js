// Simple Router for Static Site
// توجيه بسيط للموقع الثابت

document.addEventListener('DOMContentLoaded', function() {
    // تحقق من المسار الحالي
    const currentPath = window.location.pathname;
    
    // إذا كان المستخدم في صفحة غير index.html وليس مسجل دخول
    if (currentPath !== '/' && currentPath !== '/index.html' && !currentPath.includes('index.html')) {
        const currentUser = localStorage.getItem('currentUser');
        if (!currentUser) {
            // توجيه إلى صفحة تسجيل الدخول
            window.location.href = './index.html';
            return;
        }
    }
    
    // تحديث الروابط النشطة في الشريط الجانبي
    updateActiveNavigation();
});

// تحديث الروابط النشطة
function updateActiveNavigation() {
    const currentPath = window.location.pathname;
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.classList.remove('active');
        
        const href = item.getAttribute('href');
        if (href && currentPath.includes(href.replace('./', '').replace('.html', ''))) {
            item.classList.add('active');
        }
    });
    
    // تعيين الصفحة الرئيسية كنشطة إذا كنا في dashboard
    if (currentPath.includes('dashboard') || currentPath === '/') {
        const dashboardLink = document.querySelector('a[href*="dashboard"]');
        if (dashboardLink) {
            dashboardLink.classList.add('active');
        }
    }
}

// دالة للتنقل بين الصفحات
function navigateTo(page) {
    // إزالة .html إذا كان موجود
    const cleanPage = page.replace('.html', '');
    
    // التنقل إلى الصفحة
    if (cleanPage === 'index' || cleanPage === '') {
        window.location.href = './index.html';
    } else {
        window.location.href = `./${cleanPage}.html`;
    }
}

// إضافة مستمعين للروابط
document.addEventListener('click', function(e) {
    const link = e.target.closest('a[href*=".html"]');
    if (link) {
        e.preventDefault();
        const href = link.getAttribute('href');
        window.location.href = href;
    }
});

// تصدير الدوال للاستخدام العام
window.navigateTo = navigateTo;
window.updateActiveNavigation = updateActiveNavigation;
