// إصلاح البيانات المحلية لتطبيق Electron
console.log('🔧 تحميل إصلاح البيانات المحلية...');

// إعداد قاعدة بيانات محلية مع إمكانية الاتصال السحابي
let useLocalData = true;
let localData = {
    projects: [],
    sales: [],
    contractors: [],
    suppliers: [],
    purchases: [],
    users: [],
    tasks: [],
    maintenance: []
};

// تحميل البيانات من localStorage
function loadLocalData() {
    console.log('📂 تحميل البيانات المحلية...');
    
    Object.keys(localData).forEach(table => {
        const stored = localStorage.getItem(`construction_${table}`);
        if (stored) {
            try {
                localData[table] = JSON.parse(stored);
                console.log(`✅ تم تحميل ${localData[table].length} عنصر من ${table}`);
            } catch (error) {
                console.error(`❌ خطأ في تحميل ${table}:`, error);
                localData[table] = [];
            }
        }
    });
}

// حفظ البيانات في localStorage
function saveLocalData(table, data) {
    try {
        localStorage.setItem(`construction_${table}`, JSON.stringify(data));
        console.log(`💾 تم حفظ ${data.length} عنصر في ${table}`);
        return true;
    } catch (error) {
        console.error(`❌ خطأ في حفظ ${table}:`, error);
        return false;
    }
}

// إنشاء ID فريد
function generateId() {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

// إعداد قاعدة البيانات المختلطة (محلية + سحابية)
window.db = {
    isOnline: false,
    
    async get(table) {
        console.log(`📊 جلب بيانات ${table}...`);
        
        // محاولة الاتصال السحابي أولاً
        if (window.supabase && !useLocalData) {
            try {
                const { data, error } = await window.supabase
                    .from(table)
                    .select('*')
                    .order('created_at', { ascending: false });
                
                if (!error && data) {
                    console.log(`☁️ تم جلب ${data.length} عنصر من ${table} (سحابي)`);
                    this.isOnline = true;
                    updateConnectionStatus(true);
                    return data;
                }
            } catch (error) {
                console.log(`⚠️ فشل الاتصال السحابي، التبديل للمحلي`);
            }
        }
        
        // استخدام البيانات المحلية
        this.isOnline = false;
        updateConnectionStatus(false);
        const data = localData[table] || [];
        console.log(`💾 تم جلب ${data.length} عنصر من ${table} (محلي)`);
        return data;
    },
    
    async add(table, data) {
        console.log(`➕ إضافة بيانات إلى ${table}:`, data);
        
        // إضافة معلومات إضافية
        const newItem = {
            id: generateId(),
            ...data,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        
        // محاولة الحفظ السحابي أولاً
        if (window.supabase && !useLocalData) {
            try {
                const { data: result, error } = await window.supabase
                    .from(table)
                    .insert([newItem])
                    .select();
                
                if (!error && result) {
                    console.log(`☁️ تم حفظ ${table} (سحابي):`, result[0]);
                    this.isOnline = true;
                    updateConnectionStatus(true);
                    return result[0];
                }
            } catch (error) {
                console.log(`⚠️ فشل الحفظ السحابي، الحفظ محلياً`);
            }
        }
        
        // الحفظ المحلي
        this.isOnline = false;
        updateConnectionStatus(false);
        
        if (!localData[table]) {
            localData[table] = [];
        }
        
        localData[table].unshift(newItem);
        saveLocalData(table, localData[table]);
        
        console.log(`💾 تم حفظ ${table} (محلي):`, newItem);
        return newItem;
    },
    
    async update(table, id, data) {
        console.log(`🔄 تحديث ${table} ID:${id}:`, data);
        
        const updateData = {
            ...data,
            updated_at: new Date().toISOString()
        };
        
        // محاولة التحديث السحابي
        if (window.supabase && !useLocalData) {
            try {
                const { data: result, error } = await window.supabase
                    .from(table)
                    .update(updateData)
                    .eq('id', id)
                    .select();
                
                if (!error && result) {
                    console.log(`☁️ تم تحديث ${table} (سحابي):`, result[0]);
                    this.isOnline = true;
                    updateConnectionStatus(true);
                    return result[0];
                }
            } catch (error) {
                console.log(`⚠️ فشل التحديث السحابي، التحديث محلياً`);
            }
        }
        
        // التحديث المحلي
        this.isOnline = false;
        updateConnectionStatus(false);
        
        if (!localData[table]) {
            localData[table] = [];
        }
        
        const index = localData[table].findIndex(item => item.id === id);
        if (index !== -1) {
            localData[table][index] = { ...localData[table][index], ...updateData };
            saveLocalData(table, localData[table]);
            console.log(`💾 تم تحديث ${table} (محلي):`, localData[table][index]);
            return localData[table][index];
        }
        
        throw new Error('العنصر غير موجود');
    },
    
    async delete(table, id) {
        console.log(`🗑️ حذف من ${table} ID:${id}`);
        
        // محاولة الحذف السحابي
        if (window.supabase && !useLocalData) {
            try {
                const { error } = await window.supabase
                    .from(table)
                    .delete()
                    .eq('id', id);
                
                if (!error) {
                    console.log(`☁️ تم حذف ${table} (سحابي)`);
                    this.isOnline = true;
                    updateConnectionStatus(true);
                    return true;
                }
            } catch (error) {
                console.log(`⚠️ فشل الحذف السحابي، الحذف محلياً`);
            }
        }
        
        // الحذف المحلي
        this.isOnline = false;
        updateConnectionStatus(false);
        
        if (!localData[table]) {
            localData[table] = [];
        }
        
        const index = localData[table].findIndex(item => item.id === id);
        if (index !== -1) {
            localData[table].splice(index, 1);
            saveLocalData(table, localData[table]);
            console.log(`💾 تم حذف من ${table} (محلي)`);
            return true;
        }
        
        throw new Error('العنصر غير موجود');
    }
};

// تحديث حالة الاتصال
function updateConnectionStatus(isOnline) {
    const statusElements = document.querySelectorAll('.connection-status');
    statusElements.forEach(element => {
        if (isOnline) {
            element.innerHTML = '🟢 متصل (سحابي)';
            element.className = 'connection-status connected';
        } else {
            element.innerHTML = '🟡 محلي (Electron)';
            element.className = 'connection-status local';
        }
    });
}

// إضافة بيانات تجريبية إذا لم توجد
function addSampleData() {
    console.log('📝 إضافة بيانات تجريبية...');
    
    // مشاريع تجريبية
    if (localData.projects.length === 0) {
        localData.projects = [
            {
                id: generateId(),
                name: 'مشروع النيل الجديد',
                description: 'مجمع سكني متكامل',
                location: 'القاهرة الجديدة',
                status: 'planning',
                budget: 5000000,
                created_at: new Date().toISOString()
            },
            {
                id: generateId(),
                name: 'برج الأندلس',
                description: 'برج إداري وتجاري',
                location: 'المعادي',
                status: 'in_progress',
                budget: 8000000,
                created_at: new Date().toISOString()
            }
        ];
        saveLocalData('projects', localData.projects);
    }
    
    // مبيعات تجريبية
    if (localData.sales.length === 0) {
        localData.sales = [
            {
                id: generateId(),
                customer_name: 'أحمد محمد',
                apartment_number: '101',
                project_name: 'مشروع النيل الجديد',
                price: 750000,
                payment_method: 'installments',
                created_at: new Date().toISOString()
            }
        ];
        saveLocalData('sales', localData.sales);
    }
    
    // مقاولين تجريبيين
    if (localData.contractors.length === 0) {
        localData.contractors = [
            {
                id: generateId(),
                name: 'شركة البناء المتطور',
                specialty: 'أعمال الخرسانة',
                phone: '01234567890',
                email: '<EMAIL>',
                created_at: new Date().toISOString()
            }
        ];
        saveLocalData('contractors', localData.contractors);
    }
    
    console.log('✅ تم إضافة البيانات التجريبية');
}

// تبديل بين المحلي والسحابي
window.toggleDataMode = function() {
    useLocalData = !useLocalData;
    console.log(`🔄 تم التبديل إلى: ${useLocalData ? 'محلي' : 'سحابي'}`);
    updateConnectionStatus(!useLocalData);
};

// مسح البيانات المحلية
window.clearLocalData = function() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات المحلية؟')) {
        Object.keys(localData).forEach(table => {
            localStorage.removeItem(`construction_${table}`);
            localData[table] = [];
        });
        console.log('🗑️ تم مسح جميع البيانات المحلية');
        location.reload();
    }
};

// تصدير البيانات
window.exportLocalData = function() {
    const dataToExport = {
        timestamp: new Date().toISOString(),
        data: localData
    };
    
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], {
        type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `construction-data-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    
    console.log('📤 تم تصدير البيانات');
};

// تهيئة البيانات عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        console.log('🚀 تهيئة البيانات المحلية...');
        loadLocalData();
        addSampleData();
        updateConnectionStatus(false);
        
        // إصلاح الأزرار بعد تحميل البيانات
        if (typeof fixAllButtons === 'function') {
            fixAllButtons();
        }
        
        console.log('✅ تم تهيئة البيانات المحلية');
    }, 1000);
});

console.log('✅ تم تحميل إصلاح البيانات المحلية');
console.log('💡 أوامر مفيدة:');
console.log('   toggleDataMode() - تبديل بين محلي/سحابي');
console.log('   clearLocalData() - مسح البيانات المحلية');
console.log('   exportLocalData() - تصدير البيانات');
