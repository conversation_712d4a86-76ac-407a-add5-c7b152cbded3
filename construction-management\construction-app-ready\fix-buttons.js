// ملف إصلاح الأزرار لجميع الصفحات
// يجب إضافة هذا الملف لجميع الصفحات التي تحتاج إصلاح

// التأكد من وجود قاعدة البيانات
if (typeof db === 'undefined') {
    console.warn('قاعدة البيانات غير متاحة، سيتم استخدام الوضع المحلي');
    window.db = {
        isOnline: false,
        async getData(table) {
            const data = localStorage.getItem('construction_management_' + table);
            return { success: true, data: data ? JSON.parse(data) : [] };
        },
        async insertData(table, data) {
            const existingData = JSON.parse(localStorage.getItem('construction_management_' + table) || '[]');
            const newData = { ...data, id: Date.now() };
            existingData.push(newData);
            localStorage.setItem('construction_management_' + table, JSON.stringify(existingData));
            return { success: true, data: newData };
        },
        async updateData(table, id, data) {
            const existingData = JSON.parse(localStorage.getItem('construction_management_' + table) || '[]');
            const index = existingData.findIndex(item => item.id == id);
            if (index !== -1) {
                existingData[index] = { ...existingData[index], ...data };
                localStorage.setItem('construction_management_' + table, JSON.stringify(existingData));
                return { success: true, data: existingData[index] };
            }
            return { success: false, error: 'العنصر غير موجود' };
        },
        async deleteData(table, id) {
            const existingData = JSON.parse(localStorage.getItem('construction_management_' + table) || '[]');
            const filteredData = existingData.filter(item => item.id != id);
            localStorage.setItem('construction_management_' + table, JSON.stringify(filteredData));
            return { success: true };
        }
    };
}

// دالة التحقق من المصادقة (إذا لم تكن موجودة)
if (typeof checkAuthAndUpdateUI === 'undefined') {
    window.checkAuthAndUpdateUI = function() {
        const currentUser = localStorage.getItem('currentUser');
        if (!currentUser) {
            window.location.href = './index.html';
            return;
        }
        
        const user = JSON.parse(currentUser);
        const userNameEl = document.getElementById('userName');
        const userAvatarEl = document.getElementById('userAvatar');
        const userRoleEl = document.getElementById('userRole');
        
        if (userNameEl) userNameEl.textContent = user.name || 'مستخدم';
        if (userAvatarEl) userAvatarEl.textContent = (user.name || 'م').charAt(0);
        
        if (userRoleEl) {
            const roleNames = {
                admin: 'مدير النظام',
                manager: 'مدير',
                accountant: 'محاسب'
            };
            userRoleEl.textContent = roleNames[user.role] || user.role;
        }
    };
}

// دالة تسجيل الخروج (إذا لم تكن موجودة)
if (typeof logout === 'undefined') {
    window.logout = function() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            localStorage.removeItem('currentUser');
            window.location.href = './index.html';
        }
    };
}

// دوال عامة للنوافذ المنبثقة - لا تتداخل مع دوال الصفحات المحددة
if (typeof openModalGeneric === 'undefined') {
    window.openModalGeneric = function(modalId) {
        const modal = document.getElementById(modalId) || document.querySelector('.modal');
        if (modal) {
            modal.style.display = 'block';
        }
    };
}

if (typeof closeModalGeneric === 'undefined') {
    window.closeModalGeneric = function(modalId) {
        const modal = document.getElementById(modalId) || document.querySelector('.modal');
        if (modal) {
            modal.style.display = 'none';
        }
    };
}

// دوال احتياطية للنوافذ - فقط إذا لم تكن موجودة
if (typeof openModal === 'undefined') {
    window.openModal = function(modalId) {
        console.log('استخدام دالة openModal الاحتياطية');
        if (modalId) {
            openModalGeneric(modalId);
        } else {
            // البحث عن النافذة الأولى المتاحة
            const modals = document.querySelectorAll('.modal, [id*="Modal"]');
            if (modals.length > 0) {
                modals[0].style.display = 'block';
            }
        }
    };
}

if (typeof closeModal === 'undefined') {
    window.closeModal = function(modalId) {
        console.log('استخدام دالة closeModal الاحتياطية');
        if (modalId) {
            closeModalGeneric(modalId);
        } else {
            // إغلاق جميع النوافذ المفتوحة
            const modals = document.querySelectorAll('.modal, [id*="Modal"]');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
        }
    };
}

// إضافة مستمع لإغلاق النوافذ عند النقر خارجها
document.addEventListener('click', function(e) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
});

// تشغيل التحقق من المصادقة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkAuthAndUpdateUI();
});

// دالة تشخيص الأزرار
window.diagnoseButtons = function() {
    console.log('🔍 تشخيص الأزرار:');

    // فحص أزرار الإضافة
    const addButtons = document.querySelectorAll('button[onclick*="openModal"], button[onclick*="Modal"]');
    console.log(`📊 عدد أزرار الإضافة الموجودة: ${addButtons.length}`);

    addButtons.forEach((btn, index) => {
        const onclick = btn.getAttribute('onclick');
        console.log(`🔘 زر ${index + 1}: ${btn.textContent.trim()} - onclick: ${onclick}`);

        // اختبار النقر
        try {
            btn.click();
            console.log(`✅ زر ${index + 1} يعمل`);
        } catch (error) {
            console.error(`❌ زر ${index + 1} لا يعمل:`, error);
        }
    });

    // فحص النوافذ المنبثقة
    const modals = document.querySelectorAll('.modal, [id*="Modal"]');
    console.log(`🪟 عدد النوافذ المنبثقة: ${modals.length}`);

    modals.forEach((modal, index) => {
        console.log(`🪟 نافذة ${index + 1}: ID = ${modal.id}, Class = ${modal.className}`);
    });
};

// تشغيل التشخيص تلقائياً
setTimeout(() => {
    if (typeof diagnoseButtons === 'function') {
        diagnoseButtons();
    }
}, 2000);

console.log('تم تحميل ملف إصلاح الأزرار بنجاح');
