// إصلاح شامل لجميع أزرار الإضافة في التطبيق
// يجب تحميل هذا الملف بعد تحميل الصفحة

console.log('🔧 بدء إصلاح أزرار الإضافة الشامل...');

// انتظار تحميل الصفحة بالكامل
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(fixAllAddButtons, 1000);
});

// إذا كانت الصفحة محملة بالفعل
if (document.readyState === 'complete') {
    setTimeout(fixAllAddButtons, 1000);
}

function fixAllAddButtons() {
    console.log('🔧 إصلاح أزرار الإضافة...');
    
    // قائمة الصفحات وأزرارها
    const pageButtons = {
        'projects.html': {
            buttonSelector: 'button[onclick*="openModal"]',
            modalId: 'projectModal',
            formId: 'projectForm',
            titleId: 'modalTitle',
            titleText: 'مشروع جديد'
        },
        'sales.html': {
            buttonSelector: 'button[onclick*="openModal"]',
            modalId: 'saleModal',
            formId: 'saleForm',
            titleId: 'modalTitle',
            titleText: 'مبيعة جديدة'
        },
        'purchases.html': {
            buttonSelector: 'button[onclick*="openModal"]',
            modalId: 'purchaseModal',
            formId: 'purchaseForm',
            titleId: 'modalTitle',
            titleText: 'طلب شراء جديد'
        },
        'suppliers.html': {
            buttonSelector: 'button[onclick*="openSupplierModal"]',
            modalId: 'supplierModal',
            formId: 'supplierForm',
            titleId: 'supplierModalTitle',
            titleText: 'مورد جديد'
        },
        'contractors.html': {
            buttonSelector: 'button[onclick*="openModal"]',
            modalId: 'contractorModal',
            formId: 'contractorForm',
            titleId: 'modalTitle',
            titleText: 'مقاول جديد'
        }
    };
    
    // الحصول على اسم الصفحة الحالية
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    console.log(`📄 الصفحة الحالية: ${currentPage}`);
    
    // إصلاح الأزرار في الصفحة الحالية
    const pageConfig = pageButtons[currentPage];
    if (pageConfig) {
        fixPageButtons(pageConfig);
    } else {
        // إصلاح عام لجميع الأزرار
        fixGenericButtons();
    }
}

function fixPageButtons(config) {
    console.log(`🔧 إصلاح أزرار صفحة محددة...`);
    
    // البحث عن زر الإضافة
    const addButton = document.querySelector(config.buttonSelector);
    if (addButton) {
        console.log(`✅ تم العثور على زر الإضافة: ${addButton.textContent.trim()}`);
        
        // إزالة onclick القديم وإضافة جديد
        addButton.removeAttribute('onclick');
        addButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log(`🔘 تم النقر على زر الإضافة`);
            openModalSafe(config);
        });
    } else {
        console.warn(`⚠️ لم يتم العثور على زر الإضافة`);
    }
}

function fixGenericButtons() {
    console.log(`🔧 إصلاح عام للأزرار...`);
    
    // البحث عن جميع أزرار الإضافة
    const addButtons = document.querySelectorAll('button[onclick*="Modal"], button[onclick*="openModal"]');
    console.log(`📊 تم العثور على ${addButtons.length} زر إضافة`);
    
    addButtons.forEach((button, index) => {
        const originalOnclick = button.getAttribute('onclick');
        console.log(`🔘 زر ${index + 1}: ${button.textContent.trim()} - ${originalOnclick}`);
        
        // إزالة onclick القديم
        button.removeAttribute('onclick');
        
        // إضافة مستمع جديد
        button.addEventListener('click', function(e) {
            e.preventDefault();
            console.log(`🔘 تم النقر على زر: ${button.textContent.trim()}`);
            
            // محاولة تشغيل الدالة الأصلية
            try {
                if (originalOnclick) {
                    eval(originalOnclick);
                }
            } catch (error) {
                console.error(`❌ خطأ في تشغيل الدالة الأصلية:`, error);
                // محاولة فتح أول نافذة متاحة
                openFirstAvailableModal();
            }
        });
    });
}

function openModalSafe(config) {
    try {
        const modal = document.getElementById(config.modalId);
        if (modal) {
            modal.style.display = 'block';
            console.log(`✅ تم فتح النافذة: ${config.modalId}`);
            
            // تحديث العنوان
            const titleElement = document.getElementById(config.titleId);
            if (titleElement) {
                titleElement.textContent = config.titleText;
            }
            
            // إعادة تعيين النموذج
            const form = document.getElementById(config.formId);
            if (form) {
                form.reset();
            }
            
            // إعادة تعيين متغير التعديل
            if (typeof editingProjectId !== 'undefined') editingProjectId = null;
            if (typeof editingSaleId !== 'undefined') editingSaleId = null;
            if (typeof editingPurchaseId !== 'undefined') editingPurchaseId = null;
            if (typeof editingSupplierId !== 'undefined') editingSupplierId = null;
            if (typeof editingContractorId !== 'undefined') editingContractorId = null;
            
        } else {
            console.error(`❌ لم يتم العثور على النافذة: ${config.modalId}`);
        }
    } catch (error) {
        console.error(`❌ خطأ في فتح النافذة:`, error);
    }
}

function openFirstAvailableModal() {
    const modals = document.querySelectorAll('.modal, [id*="Modal"]');
    if (modals.length > 0) {
        modals[0].style.display = 'block';
        console.log(`✅ تم فتح أول نافذة متاحة: ${modals[0].id}`);
    } else {
        console.error(`❌ لم يتم العثور على أي نوافذ منبثقة`);
    }
}

// دالة تشخيص للمساعدة في اكتشاف المشاكل
window.testAddButtons = function() {
    console.log('🧪 اختبار أزرار الإضافة...');
    
    const addButtons = document.querySelectorAll('button');
    const addButtonsFiltered = Array.from(addButtons).filter(btn => 
        btn.textContent.includes('جديد') || 
        btn.textContent.includes('إضافة') ||
        btn.textContent.includes('➕')
    );
    
    console.log(`📊 تم العثور على ${addButtonsFiltered.length} زر إضافة محتمل`);
    
    addButtonsFiltered.forEach((btn, index) => {
        console.log(`🔘 زر ${index + 1}: "${btn.textContent.trim()}"`);
        console.log(`   - onclick: ${btn.getAttribute('onclick')}`);
        console.log(`   - event listeners: ${btn.onclick ? 'موجود' : 'غير موجود'}`);
    });
    
    const modals = document.querySelectorAll('.modal, [id*="Modal"]');
    console.log(`🪟 النوافذ المنبثقة المتاحة: ${modals.length}`);
    modals.forEach((modal, index) => {
        console.log(`🪟 نافذة ${index + 1}: ${modal.id} - مرئية: ${modal.style.display !== 'none'}`);
    });
};

console.log('✅ تم تحميل إصلاح أزرار الإضافة الشامل');
console.log('💡 لاختبار الأزرار، اكتب: testAddButtons() في Console');

