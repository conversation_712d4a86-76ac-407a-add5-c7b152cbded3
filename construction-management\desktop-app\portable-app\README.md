﻿# نظام إدارة المقاولات - تطبيق محمول

## طريقة التشغيل:
1. تأكد من تثبيت Node.js على الجهاز
2. شغل start.bat
3. انتظر فتح التطبيق

## متطلبات التشغيل:
- Node.js (حمل من: https://nodejs.org/)
- Windows 7/8/10/11
- 2 GB RAM على الأقل

## بيانات تسجيل الدخول:
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: admin123

## الميزات:
- إدارة المشاريع
- مبيعات الشقق
- إدارة المقاولين والمستخلصات
- إدارة الموردين والفواتير
- إدارة المشتريات
- التقارير المالية
- إدارة المستخدمين

## الدعم:
إذا واجهت أي مشاكل، تأكد من:
1. تثبيت Node.js
2. اتصال الإنترنت (لقاعدة البيانات)
3. تشغيل start.bat كمدير إذا لزم الأمر
