// إصلاح خاص لتطبيق Electron
console.log('🔧 تحميل إصلاحات Electron...');

// التأكد من تحميل كل شيء بعد تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('📱 Electron: الصفحة محملة');
    setTimeout(initializeElectronFixes, 1000);
});

// إذا كانت الصفحة محملة بالفعل
if (document.readyState === 'complete') {
    setTimeout(initializeElectronFixes, 1000);
}

function initializeElectronFixes() {
    console.log('🔧 بدء إصلاحات Electron...');
    
    // إعادة تعريف إعدادات Supabase
    window.SUPABASE_URL = 'https://xlpcpojmatiejxizukkz.supabase.co';
    window.SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhscGNwb2ptYXRpZWp4aXp1a2t6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NDg0NzksImV4cCI6MjA2NjUyNDQ3OX0.eDNwBdyHfuQLeYqX_LVHdUekyHVmA3m9PH2yu_RV9Vc';
    
    // تحميل Supabase إذا لم يكن محملاً
    if (typeof window.supabase === 'undefined') {
        loadSupabaseForElectron();
    } else {
        setupDatabase();
    }
    
    // إصلاح الأزرار
    fixAllButtons();
    
    // إصلاح النماذج
    fixAllForms();
    
    // تحديث حالة الاتصال
    updateConnectionStatus();
}

function loadSupabaseForElectron() {
    console.log('📦 تحميل Supabase للـ Electron...');
    
    // إنشاء عميل Supabase مباشرة
    if (typeof createClient !== 'undefined') {
        window.supabase = createClient(window.SUPABASE_URL, window.SUPABASE_ANON_KEY);
        console.log('✅ تم إنشاء عميل Supabase');
        setupDatabase();
    } else {
        // تحميل من CDN
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';
        script.onload = function() {
            window.supabase = window.supabase.createClient(window.SUPABASE_URL, window.SUPABASE_ANON_KEY);
            console.log('✅ تم تحميل وإنشاء عميل Supabase');
            setupDatabase();
        };
        document.head.appendChild(script);
    }
}

function setupDatabase() {
    console.log('🗄️ إعداد قاعدة البيانات...');
    
    // إعادة تعريف كائن قاعدة البيانات
    window.db = {
        isOnline: true,
        
        async get(table) {
            try {
                console.log(`📊 جلب بيانات من ${table}...`);
                const { data, error } = await window.supabase
                    .from(table)
                    .select('*')
                    .order('created_at', { ascending: false });
                
                if (error) {
                    console.error(`❌ خطأ في جلب ${table}:`, error);
                    return [];
                }
                
                console.log(`✅ تم جلب ${data.length} عنصر من ${table}`);
                return data;
            } catch (error) {
                console.error(`❌ خطأ في الاتصال بـ ${table}:`, error);
                return [];
            }
        },
        
        async add(table, data) {
            try {
                console.log(`➕ إضافة بيانات إلى ${table}:`, data);
                
                // إضافة timestamp
                const dataWithTimestamp = {
                    ...data,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };
                
                const { data: result, error } = await window.supabase
                    .from(table)
                    .insert([dataWithTimestamp])
                    .select();
                
                if (error) {
                    console.error(`❌ خطأ في إضافة ${table}:`, error);
                    throw error;
                }
                
                console.log(`✅ تم إضافة بيانات إلى ${table}:`, result[0]);
                return result[0];
            } catch (error) {
                console.error(`❌ خطأ في إضافة البيانات:`, error);
                throw error;
            }
        },
        
        async update(table, id, data) {
            try {
                console.log(`🔄 تحديث ${table} ID:${id}:`, data);
                
                const dataWithTimestamp = {
                    ...data,
                    updated_at: new Date().toISOString()
                };
                
                const { data: result, error } = await window.supabase
                    .from(table)
                    .update(dataWithTimestamp)
                    .eq('id', id)
                    .select();
                
                if (error) {
                    console.error(`❌ خطأ في تحديث ${table}:`, error);
                    throw error;
                }
                
                console.log(`✅ تم تحديث ${table}:`, result[0]);
                return result[0];
            } catch (error) {
                console.error(`❌ خطأ في تحديث البيانات:`, error);
                throw error;
            }
        },
        
        async delete(table, id) {
            try {
                console.log(`🗑️ حذف من ${table} ID:${id}`);
                
                const { error } = await window.supabase
                    .from(table)
                    .delete()
                    .eq('id', id);
                
                if (error) {
                    console.error(`❌ خطأ في حذف ${table}:`, error);
                    throw error;
                }
                
                console.log(`✅ تم حذف من ${table}`);
                return true;
            } catch (error) {
                console.error(`❌ خطأ في حذف البيانات:`, error);
                throw error;
            }
        }
    };
    
    console.log('✅ تم إعداد قاعدة البيانات');
}

function fixAllButtons() {
    console.log('🔧 إصلاح جميع الأزرار...');
    
    // البحث عن جميع أزرار الإضافة
    const addButtons = document.querySelectorAll('button[onclick*="showAdd"], button[onclick*="add"], .btn-primary');
    
    addButtons.forEach(button => {
        const originalOnclick = button.getAttribute('onclick');
        if (originalOnclick) {
            console.log('🔧 إصلاح زر:', originalOnclick);
            
            // إزالة onclick القديم
            button.removeAttribute('onclick');
            
            // إضافة event listener جديد
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                try {
                    // تنفيذ الدالة الأصلية
                    eval(originalOnclick);
                } catch (error) {
                    console.error('❌ خطأ في تنفيذ زر:', error);
                    
                    // محاولة إصلاح تلقائي
                    if (originalOnclick.includes('showAddProject')) {
                        showAddProject();
                    } else if (originalOnclick.includes('showAddSale')) {
                        showAddSale();
                    } else if (originalOnclick.includes('showAddContractor')) {
                        showAddContractor();
                    } else if (originalOnclick.includes('showAddSupplier')) {
                        showAddSupplier();
                    } else if (originalOnclick.includes('showAddPurchase')) {
                        showAddPurchase();
                    }
                }
            });
        }
    });
    
    console.log(`✅ تم إصلاح ${addButtons.length} زر`);
}

function fixAllForms() {
    console.log('📝 إصلاح جميع النماذج...');
    
    // البحث عن جميع النماذج
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            console.log('📝 إرسال نموذج:', data);
            
            // تحديد نوع النموذج وحفظ البيانات
            const formId = form.id;
            if (formId.includes('project')) {
                saveProject(data);
            } else if (formId.includes('sale')) {
                saveSale(data);
            } else if (formId.includes('contractor')) {
                saveContractor(data);
            } else if (formId.includes('supplier')) {
                saveSupplier(data);
            } else if (formId.includes('purchase')) {
                savePurchase(data);
            }
        });
    });
    
    console.log(`✅ تم إصلاح ${forms.length} نموذج`);
}

function updateConnectionStatus() {
    // تحديث جميع عناصر حالة الاتصال
    const statusElements = document.querySelectorAll('.connection-status');
    statusElements.forEach(element => {
        element.innerHTML = '🟢 متصل (Electron)';
        element.className = 'connection-status connected';
    });
    
    console.log('✅ تم تحديث حالة الاتصال');
}

// دوال حفظ البيانات
async function saveProject(data) {
    try {
        const result = await window.db.add('projects', data);
        alert('تم حفظ المشروع بنجاح!');
        if (typeof loadProjects === 'function') {
            loadProjects();
        }
        closeModal();
    } catch (error) {
        alert('خطأ في حفظ المشروع: ' + error.message);
    }
}

async function saveSale(data) {
    try {
        const result = await window.db.add('sales', data);
        alert('تم حفظ المبيعة بنجاح!');
        if (typeof loadSales === 'function') {
            loadSales();
        }
        closeModal();
    } catch (error) {
        alert('خطأ في حفظ المبيعة: ' + error.message);
    }
}

async function saveContractor(data) {
    try {
        const result = await window.db.add('contractors', data);
        alert('تم حفظ المقاول بنجاح!');
        if (typeof loadContractors === 'function') {
            loadContractors();
        }
        closeModal();
    } catch (error) {
        alert('خطأ في حفظ المقاول: ' + error.message);
    }
}

async function saveSupplier(data) {
    try {
        const result = await window.db.add('suppliers', data);
        alert('تم حفظ المورد بنجاح!');
        if (typeof loadSuppliers === 'function') {
            loadSuppliers();
        }
        closeModal();
    } catch (error) {
        alert('خطأ في حفظ المورد: ' + error.message);
    }
}

async function savePurchase(data) {
    try {
        const result = await window.db.add('purchases', data);
        alert('تم حفظ المشترى بنجاح!');
        if (typeof loadPurchases === 'function') {
            loadPurchases();
        }
        closeModal();
    } catch (error) {
        alert('خطأ في حفظ المشترى: ' + error.message);
    }
}

function closeModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
    });
}

// تشغيل الإصلاحات كل 5 ثوان للتأكد
setInterval(() => {
    if (document.querySelectorAll('button[onclick]').length > 0) {
        fixAllButtons();
    }
}, 5000);

console.log('✅ تم تحميل إصلاحات Electron');
