@echo off
chcp 65001 >nul
title إنشاء تطبيق محمول

echo.
echo ========================================
echo    إنشاء تطبيق محمول لنظام إدارة المقاولات
echo ========================================
echo.

echo 🔧 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    pause
    exit /b 1
)

echo ✅ Node.js مثبت بنجاح
echo.

echo 📦 التحقق من المكتبات...
if not exist "node_modules" (
    echo 🔄 تثبيت المكتبات المطلوبة...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المكتبات!
        pause
        exit /b 1
    )
)

echo ✅ المكتبات جاهزة
echo.

echo 🏗️ إنشاء التطبيق المحمول...

REM إنشاء مجلد التطبيق المحمول
if exist "portable-app" rmdir /s /q "portable-app"
mkdir "portable-app"

echo 📁 نسخ ملفات التطبيق...

REM نسخ الملفات الأساسية
copy "main.js" "portable-app\"
copy "preload.js" "portable-app\"
copy "package.json" "portable-app\"

REM نسخ مجلد التطبيق
xcopy "app" "portable-app\app\" /E /I /Y

REM إنشاء ملف تشغيل
echo @echo off > "portable-app\start.bat"
echo title نظام إدارة المقاولات >> "portable-app\start.bat"
echo echo بدء تشغيل نظام إدارة المقاولات... >> "portable-app\start.bat"
echo npx electron . >> "portable-app\start.bat"
echo pause >> "portable-app\start.bat"

REM إنشاء ملف README
echo # نظام إدارة المقاولات - تطبيق محمول > "portable-app\README.md"
echo. >> "portable-app\README.md"
echo ## طريقة التشغيل: >> "portable-app\README.md"
echo 1. تأكد من تثبيت Node.js >> "portable-app\README.md"
echo 2. شغل start.bat >> "portable-app\README.md"
echo 3. انتظر فتح التطبيق >> "portable-app\README.md"

echo ✅ تم إنشاء التطبيق المحمول بنجاح!
echo.
echo 📁 ستجد التطبيق في مجلد: portable-app\
echo 🚀 لتشغيل التطبيق: شغل start.bat من داخل مجلد portable-app
echo.
echo 📦 يمكنك نسخ مجلد portable-app إلى أي جهاز آخر
echo.

if exist "portable-app" (
    echo 📂 فتح مجلد التطبيق المحمول...
    explorer portable-app
)

pause
